class ModernMusicPlayer {
    constructor() {
        this.audio = document.getElementById('audio-player');
        this.currentTrack = null;
        this.tracks = [];
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadMusicLibrary();
    }

    initializeElements() {
        // Player controls
        this.playPauseBtn = document.getElementById('play-pause-btn');
        this.prevBtn = document.getElementById('prev-btn');
        this.nextBtn = document.getElementById('next-btn');
        this.shuffleBtn = document.getElementById('shuffle-btn');
        this.repeatBtn = document.getElementById('repeat-btn');
        
        // Progress elements
        this.progressBar = document.getElementById('progress-bar');
        this.progressFill = document.getElementById('progress-fill');
        this.progressHandle = document.getElementById('progress-handle');
        this.currentTimeEl = document.getElementById('current-time');
        this.totalTimeEl = document.getElementById('total-time');
        
        // Volume elements
        this.volumeBtn = document.getElementById('volume-btn');
        this.volumeRange = document.getElementById('volume-range');
        
        // Track info elements
        this.playerArtwork = document.getElementById('player-artwork');
        this.playerTrackName = document.getElementById('player-track-name');
        this.playerTrackArtist = document.getElementById('player-track-artist');
        
        // Track lists
        this.popularTracks = document.getElementById('popular-tracks');
        this.similarArtists = document.getElementById('similar-artists');
    }

    setupEventListeners() {
        // Play/pause button
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        
        // Previous/next buttons
        this.prevBtn.addEventListener('click', () => this.previousTrack());
        this.nextBtn.addEventListener('click', () => this.nextTrack());
        
        // Progress bar
        this.progressBar.addEventListener('click', (e) => this.seekTo(e));
        
        // Volume control
        this.volumeRange.addEventListener('input', (e) => this.setVolume(e.target.value));
        this.volumeBtn.addEventListener('click', () => this.toggleMute());
        
        // Audio events
        this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('ended', () => this.nextTrack());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    async loadMusicLibrary() {
        try {
            const response = await fetch('/api/music-library');
            const data = await response.json();
            this.tracks = data.tracks || [];
            this.renderTracks();
        } catch (error) {
            console.error('Failed to load music library:', error);
            this.renderSampleTracks();
        }
    }

    renderTracks() {
        if (this.tracks.length === 0) {
            this.renderSampleTracks();
            return;
        }

        // Render popular tracks
        const popularHTML = this.tracks.slice(0, 4).map((track, index) => {
            const albumArtUrl = track.albumArt ?
                (track.albumArt.type === 'file' ? track.albumArt.url : `/api/album-art/${track.id}`) :
                null;

            return `
                <div class="track-item ${index === 0 ? 'featured' : ''}" data-track-index="${index}">
                    <div class="track-artwork">
                        ${albumArtUrl ?
                            `<img src="${albumArtUrl}" alt="${track.album}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                             <i class="fas fa-music" style="display: none;"></i>` :
                            `<i class="fas fa-music"></i>`
                        }
                    </div>
                    <div class="track-info">
                        <div class="track-name">${track.title}</div>
                        <div class="track-artist">${track.artist}</div>
                    </div>
                    <div class="track-duration">${this.formatTime(track.duration || 180)}</div>
                </div>
            `;
        }).join('');

        this.popularTracks.innerHTML = popularHTML;

        // Add click listeners to tracks
        this.popularTracks.querySelectorAll('.track-item').forEach(item => {
            item.addEventListener('click', () => {
                const trackIndex = parseInt(item.dataset.trackIndex);
                this.playTrack(trackIndex);
            });
        });

        // Render similar artists
        this.renderSimilarArtists();
    }

    renderSampleTracks() {
        const sampleTracks = [
            { title: "The Internet", artist: "Come Over", duration: 241, featured: true },
            { title: "New Orleans", artist: "Various Artists", duration: 195 },
            { title: "Charcoal Baby", artist: "Blood Orange", duration: 203 },
            { title: "Girl", artist: "The Internet", duration: 186 }
        ];

        const popularHTML = sampleTracks.map((track, index) => `
            <div class="track-item ${track.featured ? 'featured' : ''}" data-track-index="${index}">
                <div class="track-artwork">
                    <i class="fas fa-music"></i>
                </div>
                <div class="track-info">
                    <div class="track-name">${track.title}</div>
                    <div class="track-artist">${track.artist}</div>
                </div>
                <div class="track-duration">${this.formatTime(track.duration)}</div>
            </div>
        `).join('');

        this.popularTracks.innerHTML = popularHTML;

        // Add click listeners
        this.popularTracks.querySelectorAll('.track-item').forEach(item => {
            item.addEventListener('click', () => {
                const trackIndex = parseInt(item.dataset.trackIndex);
                this.playTrack(trackIndex);
            });
        });

        this.renderSimilarArtists();
    }

    renderSimilarArtists() {
        const artists = [
            { name: "Tyler The Creator", avatar: "https://via.placeholder.com/50x50/ff6b6b/ffffff?text=TC" },
            { name: "Nas", avatar: "https://via.placeholder.com/50x50/4ecdc4/ffffff?text=N" },
            { name: "Wu-Tang Clan", avatar: "https://via.placeholder.com/50x50/45b7d1/ffffff?text=WT" },
            { name: "Frank Ocean", avatar: "https://via.placeholder.com/50x50/f9ca24/ffffff?text=FO" }
        ];

        const artistsHTML = artists.map(artist => `
            <div class="artist-item">
                <img src="${artist.avatar}" alt="${artist.name}" class="artist-avatar">
                <div class="artist-name">${artist.name}</div>
                <i class="fas fa-chevron-right artist-chevron"></i>
            </div>
        `).join('');

        this.similarArtists.innerHTML = artistsHTML;
    }

    playTrack(index) {
        if (this.tracks.length > 0 && this.tracks[index]) {
            this.currentTrack = this.tracks[index];
            this.audio.src = this.currentTrack.path;
            this.updateTrackInfo();
            this.play();
        } else {
            // Play sample track
            this.currentTrack = {
                title: "Sample Track",
                artist: "Demo Artist",
                path: "/sample-audio.mp3"
            };
            this.updateTrackInfo();
            // For demo purposes, we'll just update the UI
            this.isPlaying = true;
            this.updatePlayButton();
        }
    }

    togglePlayPause() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }

    play() {
        if (this.currentTrack) {
            this.audio.play().catch(e => console.log('Playback failed:', e));
            this.isPlaying = true;
            this.updatePlayButton();
        }
    }

    pause() {
        this.audio.pause();
        this.isPlaying = false;
        this.updatePlayButton();
    }

    previousTrack() {
        // Implementation for previous track
        console.log('Previous track');
    }

    nextTrack() {
        // Implementation for next track
        console.log('Next track');
    }

    updatePlayButton() {
        const icon = this.playPauseBtn.querySelector('i');
        if (this.isPlaying) {
            icon.className = 'fas fa-pause';
        } else {
            icon.className = 'fas fa-play';
        }
    }

    updateTrackInfo() {
        if (this.currentTrack) {
            this.playerTrackName.textContent = this.currentTrack.title;
            this.playerTrackArtist.textContent = this.currentTrack.artist;

            // Update album art
            const albumArtUrl = this.currentTrack.albumArt ?
                (this.currentTrack.albumArt.type === 'file' ? this.currentTrack.albumArt.url : `/api/album-art/${this.currentTrack.id}`) :
                null;

            if (albumArtUrl) {
                this.playerArtwork.src = albumArtUrl;
                this.playerArtwork.onerror = () => {
                    this.playerArtwork.src = 'https://via.placeholder.com/50x50/4A90E2/FFFFFF?text=♪';
                };
            } else {
                this.playerArtwork.src = 'https://via.placeholder.com/50x50/4A90E2/FFFFFF?text=♪';
            }
        }
    }

    updateDuration() {
        this.duration = this.audio.duration;
        this.totalTimeEl.textContent = this.formatTime(this.duration);
    }

    updateProgress() {
        this.currentTime = this.audio.currentTime;
        this.currentTimeEl.textContent = this.formatTime(this.currentTime);
        
        if (this.duration > 0) {
            const progress = (this.currentTime / this.duration) * 100;
            this.progressFill.style.width = `${progress}%`;
        }
    }

    seekTo(e) {
        const rect = this.progressBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        const seekTime = percent * this.duration;
        
        this.audio.currentTime = seekTime;
    }

    setVolume(value) {
        this.audio.volume = value / 100;
        this.updateVolumeIcon(value);
    }

    toggleMute() {
        if (this.audio.volume > 0) {
            this.audio.volume = 0;
            this.volumeRange.value = 0;
        } else {
            this.audio.volume = 1;
            this.volumeRange.value = 100;
        }
        this.updateVolumeIcon(this.volumeRange.value);
    }

    updateVolumeIcon(volume) {
        const icon = this.volumeBtn.querySelector('i');
        if (volume == 0) {
            icon.className = 'fas fa-volume-mute';
        } else if (volume < 50) {
            icon.className = 'fas fa-volume-down';
        } else {
            icon.className = 'fas fa-volume-up';
        }
    }

    handleKeyboard(e) {
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                this.previousTrack();
                break;
            case 'ArrowRight':
                this.nextTrack();
                break;
        }
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
}

// Initialize the player when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Show loading screen
    const loadingScreen = document.getElementById('loading-screen');

    // Initialize player after a short delay
    setTimeout(() => {
        new ModernMusicPlayer();

        // Hide loading screen
        setTimeout(() => {
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        }, 1000);
    }, 500);
});
