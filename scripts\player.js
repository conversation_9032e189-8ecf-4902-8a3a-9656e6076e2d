class ModernMusicPlayer {
    constructor() {
        this.audio = document.getElementById('audio-player');
        this.currentTrack = null;
        this.tracks = [];
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;

        // Core Music Features
        this.playlists = JSON.parse(localStorage.getItem('musicPlaylists')) || [];
        this.favorites = JSON.parse(localStorage.getItem('musicFavorites')) || [];
        this.recentlyPlayed = JSON.parse(localStorage.getItem('recentlyPlayed')) || [];
        this.playQueue = [];
        this.currentQueueIndex = 0;
        this.crossfadeEnabled = localStorage.getItem('crossfadeEnabled') === 'true';
        this.crossfadeDuration = parseInt(localStorage.getItem('crossfadeDuration')) || 3;
        this.playbackSpeed = parseFloat(localStorage.getItem('playbackSpeed')) || 1.0;
        this.gaplessEnabled = localStorage.getItem('gaplessEnabled') !== 'false';
        this.replayGainEnabled = localStorage.getItem('replayGainEnabled') === 'true';
        this.shuffleEnabled = localStorage.getItem('shuffleEnabled') === 'true';
        this.repeatMode = localStorage.getItem('repeatMode') || 'none'; // none, one, all
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadMusicLibrary();
        this.setupSearch();
        this.initializeAdvancedFeatures();
    }

    initializeElements() {
        // Player controls
        this.playPauseBtn = document.getElementById('play-pause-btn');
        this.prevBtn = document.getElementById('prev-btn');
        this.nextBtn = document.getElementById('next-btn');
        this.shuffleBtn = document.getElementById('shuffle-btn');
        this.repeatBtn = document.getElementById('repeat-btn');
        
        // Progress elements
        this.progressBar = document.getElementById('progress-bar');
        this.progressFill = document.getElementById('progress-fill');
        this.progressHandle = document.getElementById('progress-handle');
        this.currentTimeEl = document.getElementById('current-time');
        this.totalTimeEl = document.getElementById('total-time');
        
        // Volume elements
        this.volumeBtn = document.getElementById('volume-btn');
        this.volumeRange = document.getElementById('volume-range');
        
        // Track info elements
        this.playerArtwork = document.getElementById('player-artwork');
        this.playerTrackName = document.getElementById('player-track-name');
        this.playerTrackArtist = document.getElementById('player-track-artist');
        
        // Track lists
        this.popularTracks = document.getElementById('popular-tracks');
        this.similarArtists = document.getElementById('similar-artists');

        // Search
        this.searchInput = document.getElementById('searchInput');

        // Advanced controls
        this.speedBtn = document.getElementById('speed-btn');
        this.settingsBtn = document.getElementById('settings-btn');
        this.settingsModal = document.getElementById('settings-modal');
    }

    setupEventListeners() {
        // Play/pause button
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        
        // Previous/next buttons
        this.prevBtn.addEventListener('click', () => this.previousTrack());
        this.nextBtn.addEventListener('click', () => this.nextTrack());

        // Shuffle and repeat buttons
        if (this.shuffleBtn) {
            this.shuffleBtn.addEventListener('click', () => this.toggleShuffle());
        }
        if (this.repeatBtn) {
            this.repeatBtn.addEventListener('click', () => this.toggleRepeat());
        }
        
        // Progress bar
        this.progressBar.addEventListener('click', (e) => this.seekTo(e));
        
        // Volume control
        this.volumeRange.addEventListener('input', (e) => this.setVolume(e.target.value));
        this.volumeBtn.addEventListener('click', () => this.toggleMute());
        
        // Audio events
        this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('ended', () => this.nextTrack());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Advanced controls
        if (this.speedBtn) {
            this.speedBtn.addEventListener('click', () => this.cyclePlaybackSpeed());
        }

        if (this.settingsBtn) {
            this.settingsBtn.addEventListener('click', () => this.openSettingsModal());
        }

        // Settings modal controls
        this.setupSettingsControls();
    }

    async loadMusicLibrary() {
        try {
            const response = await fetch('/api/music-library');
            const data = await response.json();
            this.tracks = data.tracks || [];
            this.renderTracks();
            this.updateLibraryStats(data.stats);
        } catch (error) {
            console.error('Failed to load music library:', error);
            this.popularTracks.innerHTML = '<div class="no-tracks">Failed to load music library. Please check the server.</div>';
        }
    }

    renderTracks() {
        // Store original tracks for search functionality
        if (this.originalTracks.length === 0) {
            this.originalTracks = [...this.tracks];
        }

        if (this.tracks.length === 0) {
            this.popularTracks.innerHTML = '<div class="no-tracks">No music found. Please add music to the MUSIC folder.</div>';
            return;
        }

        // Show ALL tracks, not just first 4
        const popularHTML = this.tracks.map((track, index) => {
            const albumArtUrl = track.albumArt ?
                (track.albumArt.type === 'file' ? track.albumArt.url : `/api/album-art/${track.id}`) :
                null;

            return `
                <div class="track-item ${index === 0 ? 'featured' : ''}" data-track-index="${index}">
                    <div class="track-artwork">
                        ${albumArtUrl ?
                            `<img src="${albumArtUrl}" alt="${track.album}" onerror="this.parentElement.innerHTML='<i class=\\'fas fa-music\\'></i>';">` :
                            `<i class="fas fa-music"></i>`
                        }
                    </div>
                    <div class="track-info">
                        <div class="track-name">${track.title}</div>
                        <div class="track-artist">${track.artist}</div>
                    </div>
                    <div class="track-actions">
                        <button class="favorite-btn ${this.isFavorite(track.id) ? 'active' : ''}" data-track-id="${track.id}" onclick="window.musicPlayer.toggleFavorite('${track.id}')">
                            <i class="${this.isFavorite(track.id) ? 'fas' : 'far'} fa-heart"></i>
                        </button>
                        <button class="queue-add-btn" onclick="window.musicPlayer.addToQueue('${track.id}')" title="Add to Queue">
                            <i class="fas fa-plus"></i>
                        </button>
                        <div class="track-duration">${this.formatTime(track.duration || 0)}</div>
                    </div>
                </div>
            `;
        }).join('');

        this.popularTracks.innerHTML = popularHTML;

        // Add click listeners to tracks
        this.popularTracks.querySelectorAll('.track-item').forEach(item => {
            item.addEventListener('click', () => {
                const trackIndex = parseInt(item.dataset.trackIndex);
                this.playTrack(trackIndex);
            });
        });

        // Render similar artists
        this.renderSimilarArtists();
    }

    updateLibraryStats(stats) {
        if (!stats) return;

        document.getElementById('totalTracks').textContent = stats.totalTracks || 0;
        document.getElementById('totalArtists').textContent = stats.totalArtists || 0;
        document.getElementById('totalAlbums').textContent = stats.totalAlbums || 0;
    }

    setupSearch() {
        if (!this.searchInput) return;

        // Store original tracks for filtering
        this.originalTracks = [];

        // Add search event listeners
        this.searchInput.addEventListener('input', (e) => {
            this.performSearch(e.target.value);
        });

        // Clear search on escape
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.clearSearch();
            }
        });
    }

    performSearch(query) {
        if (!query || query.trim() === '') {
            this.clearSearch();
            return;
        }

        const searchTerm = query.toLowerCase().trim();
        const filteredTracks = this.originalTracks.filter(track => {
            return track.title.toLowerCase().includes(searchTerm) ||
                   track.artist.toLowerCase().includes(searchTerm) ||
                   track.album.toLowerCase().includes(searchTerm);
        });

        // Update the display with filtered results
        this.displayFilteredTracks(filteredTracks, searchTerm);
    }

    clearSearch() {
        this.searchInput.value = '';
        this.tracks = [...this.originalTracks];
        this.renderTracks();
    }

    displayFilteredTracks(filteredTracks, searchTerm) {
        if (filteredTracks.length === 0) {
            this.popularTracks.innerHTML = `
                <div class="no-tracks">
                    <i class="fas fa-search"></i>
                    <p>No tracks found for "${searchTerm}"</p>
                    <button onclick="window.musicPlayer.clearSearch()" class="clear-search-btn">Clear Search</button>
                </div>
            `;
            return;
        }

        // Render filtered tracks with search highlighting
        const tracksHTML = filteredTracks.map((track, index) => {
            const albumArtUrl = track.albumArt ?
                (track.albumArt.type === 'file' ? track.albumArt.url : `/api/album-art/${track.id}`) :
                null;

            // Highlight search terms
            const highlightText = (text, term) => {
                const regex = new RegExp(`(${term})`, 'gi');
                return text.replace(regex, '<mark>$1</mark>');
            };

            const highlightedTitle = highlightText(track.title, searchTerm);
            const highlightedArtist = highlightText(track.artist, searchTerm);
            const highlightedAlbum = highlightText(track.album, searchTerm);

            return `
                <div class="track-item" data-track-index="${this.originalTracks.indexOf(track)}">
                    <div class="track-artwork">
                        ${albumArtUrl ?
                            `<img src="${albumArtUrl}" alt="${track.album}" onerror="this.parentElement.innerHTML='<i class=\\'fas fa-music\\'></i>';">` :
                            '<i class="fas fa-music"></i>'
                        }
                    </div>
                    <div class="track-info">
                        <div class="track-name">${highlightedTitle}</div>
                        <div class="track-artist">${highlightedArtist} • ${highlightedAlbum}</div>
                    </div>
                    <div class="track-duration">${this.formatTime(track.duration)}</div>
                </div>
            `;
        }).join('');

        this.popularTracks.innerHTML = tracksHTML;

        // Add click listeners to filtered tracks
        this.popularTracks.querySelectorAll('.track-item').forEach(item => {
            item.addEventListener('click', () => {
                const trackIndex = parseInt(item.dataset.trackIndex);
                this.playTrack(trackIndex);
            });
        });
    }



    renderSimilarArtists() {
        // Get unique artists from loaded tracks
        const uniqueArtists = [...new Set(this.tracks.map(track => track.artist))];

        if (uniqueArtists.length === 0) {
            this.similarArtists.innerHTML = '<div class="no-artists">No artists found</div>';
            return;
        }

        const artistsHTML = uniqueArtists.map(artistName => {
            const artistTracks = this.tracks.filter(track => track.artist === artistName);
            const firstTrackWithArt = artistTracks.find(track => track.albumArt);
            const avatarUrl = firstTrackWithArt && firstTrackWithArt.albumArt ?
                (firstTrackWithArt.albumArt.type === 'file' ? firstTrackWithArt.albumArt.url : `/api/album-art/${firstTrackWithArt.id}`) :
                null;

            return `
                <div class="artist-item">
                    <div class="artist-avatar">
                        ${avatarUrl ?
                            `<img src="${avatarUrl}" alt="${artistName}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                             <i class="fas fa-user" style="display: none;"></i>` :
                            `<i class="fas fa-user"></i>`
                        }
                    </div>
                    <div class="artist-info">
                        <div class="artist-name">${artistName}</div>
                        <div class="artist-track-count">${artistTracks.length} track${artistTracks.length !== 1 ? 's' : ''}</div>
                    </div>
                    <i class="fas fa-chevron-right artist-chevron"></i>
                </div>
            `;
        }).join('');

        this.similarArtists.innerHTML = artistsHTML;
    }

    playTrack(index) {
        if (this.tracks.length > 0 && this.tracks[index]) {
            this.currentTrack = this.tracks[index];
            this.audio.src = this.currentTrack.url || this.currentTrack.path;

            // Add to recently played
            this.addToRecentlyPlayed(this.currentTrack.id);

            // Set playback speed
            this.audio.playbackRate = this.playbackSpeed;

            this.updateTrackInfo();
            this.play();

            // Update visual state of tracks
            document.querySelectorAll('.track-item').forEach((item, i) => {
                if (i === index) {
                    item.classList.add('playing');
                } else {
                    item.classList.remove('playing');
                }
            });
        } else {
            console.log('No track found at index:', index);
        }
    }

    togglePlayPause() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }

    play() {
        if (this.currentTrack) {
            this.audio.play().catch(e => console.log('Playback failed:', e));
            this.isPlaying = true;
            this.updatePlayButton();
        }
    }

    pause() {
        this.audio.pause();
        this.isPlaying = false;
        this.updatePlayButton();
    }

    previousTrack() {
        if (this.tracks.length === 0 || !this.currentTrack) return;

        const currentIndex = this.tracks.findIndex(track => track === this.currentTrack);
        let prevIndex;

        // Handle shuffle
        if (this.shuffleEnabled) {
            do {
                prevIndex = Math.floor(Math.random() * this.tracks.length);
            } while (prevIndex === currentIndex && this.tracks.length > 1);
        }
        // Normal sequential play
        else {
            prevIndex = currentIndex - 1;

            if (prevIndex < 0) {
                if (this.repeatMode === 'all') {
                    prevIndex = this.tracks.length - 1; // Loop to last track
                } else {
                    prevIndex = 0; // Stay at first track
                }
            }
        }

        this.playTrack(prevIndex);
    }

    nextTrack() {
        if (this.crossfadeEnabled && this.isPlaying) {
            this.crossfadeToNext();
        } else {
            this.standardNextTrack();
        }
    }

    updatePlayButton() {
        const icon = this.playPauseBtn.querySelector('i');
        if (this.isPlaying) {
            icon.className = 'fas fa-pause';
        } else {
            icon.className = 'fas fa-play';
        }
    }

    updateTrackInfo() {
        if (this.currentTrack) {
            this.playerTrackName.textContent = this.currentTrack.title;
            this.playerTrackArtist.textContent = this.currentTrack.artist;

            // Update album art
            const albumArtUrl = this.currentTrack.albumArt ?
                (this.currentTrack.albumArt.type === 'file' ? this.currentTrack.albumArt.url : `/api/album-art/${this.currentTrack.id}`) :
                null;

            if (albumArtUrl) {
                this.playerArtwork.src = albumArtUrl;
                this.playerArtwork.onerror = () => {
                    this.playerArtwork.src = 'https://via.placeholder.com/50x50/4A90E2/FFFFFF?text=♪';
                };
            } else {
                this.playerArtwork.src = 'https://via.placeholder.com/50x50/4A90E2/FFFFFF?text=♪';
            }
        }
    }

    updateDuration() {
        this.duration = this.audio.duration;
        this.totalTimeEl.textContent = this.formatTime(this.duration);
    }

    updateProgress() {
        this.currentTime = this.audio.currentTime;
        this.currentTimeEl.textContent = this.formatTime(this.currentTime);
        
        if (this.duration > 0) {
            const progress = (this.currentTime / this.duration) * 100;
            this.progressFill.style.width = `${progress}%`;
        }
    }

    seekTo(e) {
        const rect = this.progressBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        const seekTime = percent * this.duration;
        
        this.audio.currentTime = seekTime;
    }

    setVolume(value) {
        this.audio.volume = value / 100;
        this.updateVolumeIcon(value);
    }

    toggleMute() {
        if (this.audio.volume > 0) {
            this.audio.volume = 0;
            this.volumeRange.value = 0;
        } else {
            this.audio.volume = 1;
            this.volumeRange.value = 100;
        }
        this.updateVolumeIcon(this.volumeRange.value);
    }

    updateVolumeIcon(volume) {
        const icon = this.volumeBtn.querySelector('i');
        if (volume == 0) {
            icon.className = 'fas fa-volume-mute';
        } else if (volume < 50) {
            icon.className = 'fas fa-volume-down';
        } else {
            icon.className = 'fas fa-volume-up';
        }
    }

    // 🎵 SHUFFLE AND REPEAT FUNCTIONALITY
    toggleShuffle() {
        this.shuffleEnabled = !this.shuffleEnabled;
        localStorage.setItem('shuffleEnabled', this.shuffleEnabled.toString());

        // Update button appearance
        if (this.shuffleBtn) {
            this.shuffleBtn.classList.toggle('active', this.shuffleEnabled);
        }

        console.log('Shuffle:', this.shuffleEnabled ? 'ON' : 'OFF');
    }

    toggleRepeat() {
        const modes = ['none', 'one', 'all'];
        const currentIndex = modes.indexOf(this.repeatMode);
        const nextIndex = (currentIndex + 1) % modes.length;
        this.repeatMode = modes[nextIndex];

        localStorage.setItem('repeatMode', this.repeatMode);

        // Update button appearance
        if (this.repeatBtn) {
            const icon = this.repeatBtn.querySelector('i');
            this.repeatBtn.classList.remove('active', 'repeat-one');

            switch (this.repeatMode) {
                case 'one':
                    this.repeatBtn.classList.add('active', 'repeat-one');
                    icon.className = 'fas fa-redo-alt';
                    break;
                case 'all':
                    this.repeatBtn.classList.add('active');
                    icon.className = 'fas fa-redo-alt';
                    break;
                default:
                    icon.className = 'fas fa-redo-alt';
                    break;
            }
        }

        console.log('Repeat mode:', this.repeatMode);
    }

    handleKeyboard(e) {
        // Don't handle shortcuts when typing in inputs
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        switch(e.code) {
            case 'Space':
                e.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                this.previousTrack();
                break;
            case 'ArrowRight':
                this.nextTrack();
                break;
            case 'KeyS':
                if (e.ctrlKey || e.metaKey) return; // Don't interfere with save
                this.toggleShuffle();
                break;
            case 'KeyR':
                if (e.ctrlKey || e.metaKey) return; // Don't interfere with refresh
                this.toggleRepeat();
                break;
            case 'KeyM':
                this.toggleMute();
                break;
            case 'ArrowUp':
                e.preventDefault();
                const currentVol = parseInt(this.volumeRange.value);
                this.setVolume(Math.min(100, currentVol + 10));
                this.volumeRange.value = Math.min(100, currentVol + 10);
                break;
            case 'ArrowDown':
                e.preventDefault();
                const currentVolDown = parseInt(this.volumeRange.value);
                this.setVolume(Math.max(0, currentVolDown - 10));
                this.volumeRange.value = Math.max(0, currentVolDown - 10);
                break;
        }
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    // 🎵 PLAYLIST MANAGEMENT
    createPlaylist(name, description = '') {
        const playlist = {
            id: Date.now().toString(),
            name: name,
            description: description,
            tracks: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.playlists.push(playlist);
        this.savePlaylistsToStorage();
        this.renderPlaylists();
        return playlist;
    }

    addToPlaylist(playlistId, trackId) {
        const playlist = this.playlists.find(p => p.id === playlistId);
        if (playlist && !playlist.tracks.includes(trackId)) {
            playlist.tracks.push(trackId);
            playlist.updatedAt = new Date().toISOString();
            this.savePlaylistsToStorage();
            this.renderPlaylists();
        }
    }

    removeFromPlaylist(playlistId, trackId) {
        const playlist = this.playlists.find(p => p.id === playlistId);
        if (playlist) {
            playlist.tracks = playlist.tracks.filter(id => id !== trackId);
            playlist.updatedAt = new Date().toISOString();
            this.savePlaylistsToStorage();
            this.renderPlaylists();
        }
    }

    deletePlaylist(playlistId) {
        this.playlists = this.playlists.filter(p => p.id !== playlistId);
        this.savePlaylistsToStorage();
        this.renderPlaylists();
    }

    savePlaylistsToStorage() {
        localStorage.setItem('musicPlaylists', JSON.stringify(this.playlists));
    }

    renderPlaylists() {
        // This would render playlists in a dedicated UI section
        // For now, just log the playlists
        console.log('Playlists updated:', this.playlists);
    }

    // 🎵 FAVORITES SYSTEM
    toggleFavorite(trackId) {
        const index = this.favorites.indexOf(trackId);
        if (index === -1) {
            this.favorites.push(trackId);
        } else {
            this.favorites.splice(index, 1);
        }
        localStorage.setItem('musicFavorites', JSON.stringify(this.favorites));
        this.updateFavoriteButtons();
    }

    isFavorite(trackId) {
        return this.favorites.includes(trackId);
    }

    updateFavoriteButtons() {
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            const trackId = btn.dataset.trackId;
            const isFav = this.isFavorite(trackId);
            btn.classList.toggle('active', isFav);
            btn.innerHTML = isFav ? '<i class="fas fa-heart"></i>' : '<i class="far fa-heart"></i>';
        });
    }

    // 🎵 RECENTLY PLAYED
    addToRecentlyPlayed(trackId) {
        // Remove if already exists
        this.recentlyPlayed = this.recentlyPlayed.filter(item => item.trackId !== trackId);

        // Add to beginning
        this.recentlyPlayed.unshift({
            trackId: trackId,
            playedAt: new Date().toISOString()
        });

        // Keep only last 50 tracks
        this.recentlyPlayed = this.recentlyPlayed.slice(0, 50);

        localStorage.setItem('recentlyPlayed', JSON.stringify(this.recentlyPlayed));
    }

    // 🎵 QUEUE MANAGEMENT
    addToQueue(trackId, position = 'end') {
        if (position === 'next') {
            this.playQueue.splice(this.currentQueueIndex + 1, 0, trackId);
        } else {
            this.playQueue.push(trackId);
        }
        this.updateQueueDisplay();
    }

    removeFromQueue(index) {
        if (index < this.currentQueueIndex) {
            this.currentQueueIndex--;
        }
        this.playQueue.splice(index, 1);
        this.updateQueueDisplay();
    }

    clearQueue() {
        this.playQueue = [];
        this.currentQueueIndex = 0;
        this.updateQueueDisplay();
    }

    playFromQueue(index) {
        this.currentQueueIndex = index;
        const trackId = this.playQueue[index];
        const trackIndex = this.originalTracks.findIndex(t => t.id === trackId);
        if (trackIndex !== -1) {
            this.playTrack(trackIndex);
        }
    }

    updateQueueDisplay() {
        const queueContainer = document.getElementById('queue-list');
        if (!queueContainer) return;

        if (this.playQueue.length === 0) {
            queueContainer.innerHTML = '<div class="empty-queue">Queue is empty</div>';
            return;
        }

        const queueHTML = this.playQueue.map((trackId, index) => {
            const track = this.originalTracks.find(t => t.id === trackId);
            if (!track) return '';

            const isCurrentTrack = index === this.currentQueueIndex;

            return `
                <div class="queue-item ${isCurrentTrack ? 'current' : ''}" data-index="${index}">
                    <div class="queue-track-info">
                        <div class="queue-track-name">${track.title}</div>
                        <div class="queue-track-artist">${track.artist}</div>
                    </div>
                    <div class="queue-actions">
                        <button class="queue-play-btn" onclick="window.musicPlayer.playFromQueue(${index})">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="queue-remove-btn" onclick="window.musicPlayer.removeFromQueue(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        queueContainer.innerHTML = queueHTML;
    }

    // 🎵 ADVANCED PLAYBACK FEATURES
    cyclePlaybackSpeed() {
        const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
        const currentIndex = speeds.indexOf(this.playbackSpeed);
        const nextIndex = (currentIndex + 1) % speeds.length;

        this.playbackSpeed = speeds[nextIndex];
        this.audio.playbackRate = this.playbackSpeed;

        // Update UI
        const speedText = this.speedBtn.querySelector('.speed-text');
        speedText.textContent = `${this.playbackSpeed}x`;

        // Save to localStorage
        localStorage.setItem('playbackSpeed', this.playbackSpeed.toString());
    }

    openSettingsModal() {
        if (this.settingsModal) {
            this.settingsModal.classList.remove('hidden');
            this.loadSettingsValues();
        }
    }

    closeSettingsModal() {
        if (this.settingsModal) {
            this.settingsModal.classList.add('hidden');
        }
    }

    setupSettingsControls() {
        // Crossfade controls
        const crossfadeEnabled = document.getElementById('crossfade-enabled');
        const crossfadeDuration = document.getElementById('crossfade-duration');
        const crossfadeValue = document.getElementById('crossfade-value');

        if (crossfadeEnabled) {
            crossfadeEnabled.addEventListener('change', (e) => {
                this.crossfadeEnabled = e.target.checked;
                localStorage.setItem('crossfadeEnabled', this.crossfadeEnabled.toString());
            });
        }

        if (crossfadeDuration) {
            crossfadeDuration.addEventListener('input', (e) => {
                this.crossfadeDuration = parseInt(e.target.value);
                crossfadeValue.textContent = this.crossfadeDuration;
                localStorage.setItem('crossfadeDuration', this.crossfadeDuration.toString());
            });
        }

        // Gapless playback
        const gaplessEnabled = document.getElementById('gapless-enabled');
        if (gaplessEnabled) {
            gaplessEnabled.addEventListener('change', (e) => {
                this.gaplessEnabled = e.target.checked;
                localStorage.setItem('gaplessEnabled', this.gaplessEnabled.toString());
            });
        }

        // Replay gain
        const replayGainEnabled = document.getElementById('replay-gain-enabled');
        if (replayGainEnabled) {
            replayGainEnabled.addEventListener('change', (e) => {
                this.replayGainEnabled = e.target.checked;
                localStorage.setItem('replayGainEnabled', this.replayGainEnabled.toString());
            });
        }

        // Modal close on outside click
        if (this.settingsModal) {
            this.settingsModal.addEventListener('click', (e) => {
                if (e.target === this.settingsModal) {
                    this.closeSettingsModal();
                }
            });
        }
    }

    loadSettingsValues() {
        // Load current settings into modal
        const crossfadeEnabled = document.getElementById('crossfade-enabled');
        const crossfadeDuration = document.getElementById('crossfade-duration');
        const crossfadeValue = document.getElementById('crossfade-value');
        const gaplessEnabled = document.getElementById('gapless-enabled');
        const replayGainEnabled = document.getElementById('replay-gain-enabled');

        if (crossfadeEnabled) crossfadeEnabled.checked = this.crossfadeEnabled;
        if (crossfadeDuration) {
            crossfadeDuration.value = this.crossfadeDuration;
            crossfadeValue.textContent = this.crossfadeDuration;
        }
        if (gaplessEnabled) gaplessEnabled.checked = this.gaplessEnabled;
        if (replayGainEnabled) replayGainEnabled.checked = this.replayGainEnabled;
    }



    crossfadeToNext() {
        const currentVolume = this.audio.volume;
        const fadeSteps = 20;
        const fadeInterval = (this.crossfadeDuration * 1000) / fadeSteps;
        let step = 0;

        const fadeOut = setInterval(() => {
            step++;
            this.audio.volume = currentVolume * (1 - step / fadeSteps);

            if (step >= fadeSteps) {
                clearInterval(fadeOut);
                this.standardNextTrack();

                // Fade in new track
                let fadeInStep = 0;
                const fadeIn = setInterval(() => {
                    fadeInStep++;
                    this.audio.volume = currentVolume * (fadeInStep / fadeSteps);

                    if (fadeInStep >= fadeSteps) {
                        clearInterval(fadeIn);
                        this.audio.volume = currentVolume;
                    }
                }, fadeInterval);
            }
        }, fadeInterval);
    }

    standardNextTrack() {
        if (this.tracks.length === 0) return;

        const currentIndex = this.tracks.findIndex(track => track === this.currentTrack);
        let nextIndex;

        // Handle repeat one
        if (this.repeatMode === 'one') {
            nextIndex = currentIndex;
        }
        // Handle shuffle
        else if (this.shuffleEnabled) {
            do {
                nextIndex = Math.floor(Math.random() * this.tracks.length);
            } while (nextIndex === currentIndex && this.tracks.length > 1);
        }
        // Normal sequential play
        else {
            nextIndex = currentIndex + 1;

            // Handle end of playlist
            if (nextIndex >= this.tracks.length) {
                if (this.repeatMode === 'all') {
                    nextIndex = 0; // Loop to first track
                } else {
                    // Stop playing if no repeat
                    this.pause();
                    return;
                }
            }
        }

        this.playTrack(nextIndex);
    }

    initializeAdvancedFeatures() {
        // Initialize speed button text
        if (this.speedBtn) {
            const speedText = this.speedBtn.querySelector('.speed-text');
            if (speedText) {
                speedText.textContent = `${this.playbackSpeed}x`;
            }
        }

        // Initialize shuffle button state
        if (this.shuffleBtn) {
            this.shuffleBtn.classList.toggle('active', this.shuffleEnabled);
        }

        // Initialize repeat button state
        if (this.repeatBtn) {
            const icon = this.repeatBtn.querySelector('i');
            this.repeatBtn.classList.remove('active', 'repeat-one');

            switch (this.repeatMode) {
                case 'one':
                    this.repeatBtn.classList.add('active', 'repeat-one');
                    break;
                case 'all':
                    this.repeatBtn.classList.add('active');
                    break;
            }
        }

        // Initialize audio context for future equalizer implementation
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.audioSource = null;
            this.gainNode = this.audioContext.createGain();
        } catch (e) {
            console.warn('Web Audio API not supported');
        }
    }
}

// Initialize the player when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Show loading screen
    const loadingScreen = document.getElementById('loading-screen');

    // Initialize player after a short delay
    setTimeout(() => {
        window.musicPlayer = new ModernMusicPlayer();

        // Hide loading screen
        setTimeout(() => {
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        }, 1000);
    }, 500);
});
