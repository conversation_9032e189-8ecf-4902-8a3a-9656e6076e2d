// ===== CONSTANTS & CONFIGURATION =====

// Application Configuration
export const APP_CONFIG = {
    name: 'Nexus Music Player',
    version: '2.0.0',
    author: 'Advanced Music Systems',
    
    // Audio Settings
    audio: {
        crossfadeTime: 3000, // 3 seconds
        defaultVolume: 0.7,
        maxVolume: 1.0,
        fadeInTime: 1000,
        fadeOutTime: 1000,
        bufferSize: 4096,
        sampleRate: 44100
    },
    
    // Visualizer Settings
    visualizer: {
        fftSize: 2048,
        smoothingTimeConstant: 0.8,
        minDecibels: -90,
        maxDecibels: -10,
        refreshRate: 60, // FPS
        modes: ['bars', 'wave', 'circle', 'particles']
    },
    
    // UI Settings
    ui: {
        animationDuration: 300,
        debounceDelay: 300,
        searchMinLength: 2,
        maxRecentTracks: 10,
        maxQueueSize: 1000,
        autoSaveInterval: 30000 // 30 seconds
    },
    
    // Storage Keys
    storage: {
        library: 'nexus_music_library',
        playlists: 'nexus_playlists',
        settings: 'nexus_settings',
        queue: 'nexus_queue',
        currentTrack: 'nexus_current_track',
        recentTracks: 'nexus_recent_tracks',
        favorites: 'nexus_favorites'
    }
};

// Supported Audio Formats
export const SUPPORTED_FORMATS = [
    'audio/mpeg',      // MP3
    'audio/mp4',       // M4A, AAC
    'audio/ogg',       // OGG
    'audio/wav',       // WAV
    'audio/flac',      // FLAC
    'audio/webm',      // WebM Audio
    'audio/aac',       // AAC
    'audio/x-m4a'      // M4A
];

// Supported Image Formats for Album Art
export const SUPPORTED_IMAGE_FORMATS = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp'
];

// File Extensions
export const AUDIO_EXTENSIONS = [
    '.mp3', '.m4a', '.aac', '.ogg', '.wav', '.flac', '.webm'
];

export const IMAGE_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'
];

// Keyboard Shortcuts
export const KEYBOARD_SHORTCUTS = {
    // Playback
    PLAY_PAUSE: ' ', // Spacebar
    NEXT_TRACK: 'ArrowRight',
    PREV_TRACK: 'ArrowLeft',
    VOLUME_UP: 'ArrowUp',
    VOLUME_DOWN: 'ArrowDown',
    MUTE: 'm',
    
    // Navigation
    SEARCH: '/',
    ESCAPE: 'Escape',
    
    // Modifiers
    SHUFFLE: 's',
    REPEAT: 'r',
    FAVORITE: 'f',
    
    // UI
    FULLSCREEN: 'F11',
    TOGGLE_SIDEBAR: 'b',
    TOGGLE_PANEL: 'p',
    
    // Queue
    ADD_TO_QUEUE: 'q',
    CLEAR_QUEUE: 'Shift+Delete'
};

// Default Settings
export const DEFAULT_SETTINGS = {
    // Audio
    volume: 0.7,
    muted: false,
    crossfade: true,
    gaplessPlayback: true,
    replayGain: false,
    
    // Playback
    shuffle: false,
    repeat: 'none', // 'none', 'one', 'all'
    autoplay: true,
    
    // UI
    theme: 'dark',
    sidebarCollapsed: false,
    rightPanelVisible: true,
    visualizerMode: 'bars',
    showLyrics: true,
    
    // Library
    sortBy: 'title',
    sortOrder: 'asc',
    viewMode: 'grid',
    groupBy: 'none',
    
    // Advanced
    hardwareAcceleration: true,
    preloadNext: true,
    scrobbling: false,
    notifications: true
};

// Error Messages
export const ERROR_MESSAGES = {
    AUDIO_LOAD_FAILED: 'Failed to load audio file',
    AUDIO_PLAY_FAILED: 'Failed to play audio',
    AUDIO_CONTEXT_FAILED: 'Failed to initialize audio context',
    FILE_NOT_SUPPORTED: 'File format not supported',
    LIBRARY_LOAD_FAILED: 'Failed to load music library',
    STORAGE_FAILED: 'Failed to save data',
    NETWORK_ERROR: 'Network error occurred',
    PERMISSION_DENIED: 'Permission denied',
    QUOTA_EXCEEDED: 'Storage quota exceeded'
};

// Success Messages
export const SUCCESS_MESSAGES = {
    LIBRARY_LOADED: 'Music library loaded successfully',
    PLAYLIST_CREATED: 'Playlist created successfully',
    TRACK_ADDED: 'Track added to playlist',
    SETTINGS_SAVED: 'Settings saved successfully',
    EXPORT_COMPLETE: 'Export completed successfully'
};

// API Endpoints (for future features)
export const API_ENDPOINTS = {
    LYRICS: 'https://api.lyrics.ovh/v1',
    METADATA: 'https://api.musicbrainz.org/ws/2',
    ARTWORK: 'https://coverartarchive.org/release'
};

// Regular Expressions
export const REGEX_PATTERNS = {
    TRACK_NUMBER: /^(\d+)[\s\-\.]*(.+)$/,
    ARTIST_TITLE: /^(.+?)\s*[\-–—]\s*(.+)$/,
    FEAT_ARTIST: /^(.+?)\s*(?:\(|\[)?(?:feat\.?|featuring|ft\.?)\s*([^)\]]+)(?:\)|\])?$/i,
    TIME_FORMAT: /^(\d{1,2}):([0-5]\d)$/,
    YEAR: /\b(19|20)\d{2}\b/
};

// Animation Easing Functions
export const EASING = {
    LINEAR: 'linear',
    EASE: 'ease',
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
    CUBIC_BEZIER: 'cubic-bezier(0.4, 0, 0.2, 1)'
};

// Color Schemes
export const COLOR_SCHEMES = {
    DARK: {
        primary: '#6366f1',
        background: '#0f0f0f',
        surface: '#1a1a1a',
        text: '#ffffff'
    },
    LIGHT: {
        primary: '#6366f1',
        background: '#ffffff',
        surface: '#f8fafc',
        text: '#1f2937'
    },
    PURPLE: {
        primary: '#8b5cf6',
        background: '#0f0a1a',
        surface: '#1a0f2e',
        text: '#ffffff'
    },
    BLUE: {
        primary: '#3b82f6',
        background: '#0a0f1a',
        surface: '#0f1a2e',
        text: '#ffffff'
    }
};

// Visualizer Configurations
export const VISUALIZER_CONFIGS = {
    bars: {
        barCount: 64,
        barWidth: 4,
        barSpacing: 2,
        smoothing: 0.8,
        amplification: 1.5
    },
    wave: {
        lineWidth: 3,
        smoothing: 0.9,
        amplification: 1.2,
        frequency: 2
    },
    circle: {
        radius: 100,
        lineWidth: 2,
        smoothing: 0.8,
        amplification: 1.0,
        rotation: true
    },
    particles: {
        count: 100,
        size: 2,
        speed: 1,
        gravity: 0.1,
        bounce: 0.8
    }
};

// Performance Monitoring
export const PERFORMANCE_METRICS = {
    AUDIO_LATENCY: 'audio_latency',
    RENDER_TIME: 'render_time',
    MEMORY_USAGE: 'memory_usage',
    CPU_USAGE: 'cpu_usage',
    FRAME_RATE: 'frame_rate'
};

// Feature Flags
export const FEATURE_FLAGS = {
    ADVANCED_VISUALIZER: true,
    LYRICS_SYNC: true,
    CROSSFADE: true,
    EQUALIZER: true,
    SCROBBLING: false,
    CLOUD_SYNC: false,
    AI_RECOMMENDATIONS: false
};
