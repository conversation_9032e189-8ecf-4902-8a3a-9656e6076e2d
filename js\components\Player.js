// ===== PLAYER COMPONENT =====

import { APP_CONFIG, KEYBOARD_SHORTCUTS } from '../utils/constants.js';
import { TimeUtils, DOMUtils } from '../utils/helpers.js';

export class Player {
    constructor(audioEngine, playbackManager, musicLibrary) {
        this.audioEngine = audioEngine;
        this.playbackManager = playbackManager;
        this.musicLibrary = musicLibrary;
        
        // UI Elements
        this.playerBar = null;
        this.playPauseBtn = null;
        this.prevBtn = null;
        this.nextBtn = null;
        this.shuffleBtn = null;
        this.repeatBtn = null;
        this.favoriteBtn = null;
        this.progressBar = null;
        this.progressFill = null;
        this.progressHandle = null;
        this.volumeSlider = null;
        this.volumeFill = null;
        this.volumeHandle = null;
        this.currentTimeDisplay = null;
        this.totalTimeDisplay = null;
        this.trackTitle = null;
        this.trackArtist = null;
        this.trackArtwork = null;
        
        // State
        this.isDraggingProgress = false;
        this.isDraggingVolume = false;
        this.currentTrack = null;
        
        this.initialize();
    }

    /**
     * Initialize player component
     */
    initialize() {
        this.setupElements();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.updateUI();
        
        console.log('Player component initialized');
    }

    /**
     * Setup UI elements
     */
    setupElements() {
        this.playerBar = document.getElementById('player-bar');
        this.playPauseBtn = document.getElementById('play-pause-btn');
        this.prevBtn = document.getElementById('prev-btn');
        this.nextBtn = document.getElementById('next-btn');
        this.shuffleBtn = document.getElementById('shuffle-btn');
        this.repeatBtn = document.getElementById('repeat-btn');
        this.favoriteBtn = document.getElementById('favorite-btn');
        this.progressBar = document.getElementById('progress-bar');
        this.progressFill = document.getElementById('progress-fill');
        this.progressHandle = document.getElementById('progress-handle');
        this.volumeSlider = document.getElementById('volume-slider');
        this.volumeFill = document.getElementById('volume-fill');
        this.volumeHandle = document.getElementById('volume-handle');
        this.currentTimeDisplay = document.getElementById('current-time');
        this.totalTimeDisplay = document.getElementById('total-time');
        this.trackTitle = document.getElementById('current-title');
        this.trackArtist = document.getElementById('current-artist');
        this.trackArtwork = document.getElementById('current-artwork');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Playback controls
        this.playPauseBtn?.addEventListener('click', () => this.togglePlayPause());
        this.prevBtn?.addEventListener('click', () => this.playPrevious());
        this.nextBtn?.addEventListener('click', () => this.playNext());
        this.shuffleBtn?.addEventListener('click', () => this.toggleShuffle());
        this.repeatBtn?.addEventListener('click', () => this.cycleRepeat());
        this.favoriteBtn?.addEventListener('click', () => this.toggleFavorite());
        
        // Progress bar
        this.setupProgressBar();
        
        // Volume control
        this.setupVolumeControl();
        
        // Audio engine events
        this.audioEngine.addEventListener('play', () => this.updatePlayPauseButton(true));
        this.audioEngine.addEventListener('pause', () => this.updatePlayPauseButton(false));
        this.audioEngine.addEventListener('timeupdate', (e) => this.updateProgress(e.detail));
        this.audioEngine.addEventListener('loadedmetadata', (e) => this.updateDuration(e.detail));
        this.audioEngine.addEventListener('volumechange', (e) => this.updateVolumeDisplay(e.detail));
        
        // Playback manager events
        this.playbackManager.addEventListener('trackChanged', (e) => this.updateTrackInfo(e.detail));
        this.playbackManager.addEventListener('shuffleChanged', (e) => this.updateShuffleButton(e.detail));
        this.playbackManager.addEventListener('repeatChanged', (e) => this.updateRepeatButton(e.detail));
        this.playbackManager.addEventListener('queueChanged', () => this.updateNavigationButtons());
        
        // Music library events
        this.musicLibrary.addEventListener('favoritesChanged', (e) => this.updateFavoriteButton(e.detail));
    }

    /**
     * Setup progress bar interactions
     */
    setupProgressBar() {
        if (!this.progressBar) return;
        
        const handleProgressInteraction = (e) => {
            const rect = this.progressBar.getBoundingClientRect();
            const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
            const duration = this.audioEngine.getState().duration;
            
            if (duration) {
                const seekTime = percent * duration;
                this.audioEngine.seek(seekTime);
            }
        };
        
        // Mouse events
        this.progressBar.addEventListener('mousedown', (e) => {
            this.isDraggingProgress = true;
            handleProgressInteraction(e);
        });
        
        document.addEventListener('mousemove', (e) => {
            if (this.isDraggingProgress) {
                handleProgressInteraction(e);
            }
        });
        
        document.addEventListener('mouseup', () => {
            this.isDraggingProgress = false;
        });
        
        // Touch events for mobile
        this.progressBar.addEventListener('touchstart', (e) => {
            this.isDraggingProgress = true;
            const touch = e.touches[0];
            handleProgressInteraction(touch);
        });
        
        document.addEventListener('touchmove', (e) => {
            if (this.isDraggingProgress) {
                const touch = e.touches[0];
                handleProgressInteraction(touch);
            }
        });
        
        document.addEventListener('touchend', () => {
            this.isDraggingProgress = false;
        });
    }

    /**
     * Setup volume control interactions
     */
    setupVolumeControl() {
        if (!this.volumeSlider) return;
        
        const handleVolumeInteraction = (e) => {
            const rect = this.volumeSlider.getBoundingClientRect();
            const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
            this.audioEngine.setVolume(percent);
        };
        
        // Mouse events
        this.volumeSlider.addEventListener('mousedown', (e) => {
            this.isDraggingVolume = true;
            handleVolumeInteraction(e);
        });
        
        document.addEventListener('mousemove', (e) => {
            if (this.isDraggingVolume) {
                handleVolumeInteraction(e);
            }
        });
        
        document.addEventListener('mouseup', () => {
            this.isDraggingVolume = false;
        });
        
        // Volume button for mute toggle
        const volumeBtn = document.getElementById('volume-btn');
        volumeBtn?.addEventListener('click', () => {
            this.audioEngine.toggleMute();
        });
    }

    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Don't trigger shortcuts when typing in inputs
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }
            
            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'ArrowLeft':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.playPrevious();
                    }
                    break;
                case 'ArrowRight':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.playNext();
                    }
                    break;
                case 'ArrowUp':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.adjustVolume(0.1);
                    }
                    break;
                case 'ArrowDown':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.adjustVolume(-0.1);
                    }
                    break;
                case 'KeyM':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.audioEngine.toggleMute();
                    }
                    break;
                case 'KeyS':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.toggleShuffle();
                    }
                    break;
                case 'KeyR':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.cycleRepeat();
                    }
                    break;
                case 'KeyF':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.toggleFavorite();
                    }
                    break;
            }
        });
    }

    /**
     * Toggle play/pause
     */
    async togglePlayPause() {
        try {
            if (this.audioEngine.isPlaying) {
                this.audioEngine.pause();
            } else {
                if (!this.currentTrack && this.playbackManager.queue.length > 0) {
                    // Start playing first track in queue
                    await this.playbackManager.playTrack(this.playbackManager.queue[0]);
                } else {
                    await this.audioEngine.play();
                }
            }
        } catch (error) {
            console.error('Failed to toggle playback:', error);
        }
    }

    /**
     * Play previous track
     */
    async playPrevious() {
        try {
            await this.playbackManager.playPrevious();
        } catch (error) {
            console.error('Failed to play previous track:', error);
        }
    }

    /**
     * Play next track
     */
    async playNext() {
        try {
            await this.playbackManager.playNext();
        } catch (error) {
            console.error('Failed to play next track:', error);
        }
    }

    /**
     * Toggle shuffle mode
     */
    toggleShuffle() {
        this.playbackManager.toggleShuffle();
    }

    /**
     * Cycle repeat mode
     */
    cycleRepeat() {
        this.playbackManager.cycleRepeatMode();
    }

    /**
     * Toggle favorite status of current track
     */
    toggleFavorite() {
        if (this.currentTrack) {
            const isFavorite = this.musicLibrary.isFavorite(this.currentTrack.id);
            if (isFavorite) {
                this.musicLibrary.removeFromFavorites(this.currentTrack.id);
            } else {
                this.musicLibrary.addToFavorites(this.currentTrack.id);
            }
        }
    }

    /**
     * Adjust volume by delta
     */
    adjustVolume(delta) {
        const currentVolume = this.audioEngine.volume;
        const newVolume = Math.max(0, Math.min(1, currentVolume + delta));
        this.audioEngine.setVolume(newVolume);
    }

    /**
     * Update play/pause button
     */
    updatePlayPauseButton(isPlaying) {
        if (this.playPauseBtn) {
            const icon = this.playPauseBtn.querySelector('i');
            if (icon) {
                icon.className = isPlaying ? 'fas fa-pause' : 'fas fa-play';
            }
            this.playPauseBtn.title = isPlaying ? 'Pause' : 'Play';
        }
    }

    /**
     * Update progress bar
     */
    updateProgress(timeData) {
        if (this.isDraggingProgress) return;
        
        const { currentTime, duration, progress } = timeData;
        
        if (this.progressFill) {
            this.progressFill.style.width = `${progress * 100}%`;
        }
        
        if (this.currentTimeDisplay) {
            this.currentTimeDisplay.textContent = TimeUtils.formatTime(currentTime);
        }
    }

    /**
     * Update duration display
     */
    updateDuration(metaData) {
        if (this.totalTimeDisplay) {
            this.totalTimeDisplay.textContent = TimeUtils.formatTime(metaData.duration);
        }
    }

    /**
     * Update volume display
     */
    updateVolumeDisplay(volumeData) {
        if (this.volumeFill) {
            this.volumeFill.style.width = `${volumeData.volume * 100}%`;
        }
        
        const volumeBtn = document.getElementById('volume-btn');
        if (volumeBtn) {
            const icon = volumeBtn.querySelector('i');
            if (icon) {
                if (volumeData.muted || volumeData.volume === 0) {
                    icon.className = 'fas fa-volume-mute';
                } else if (volumeData.volume < 0.5) {
                    icon.className = 'fas fa-volume-down';
                } else {
                    icon.className = 'fas fa-volume-up';
                }
            }
        }
    }

    /**
     * Update track information display
     */
    updateTrackInfo(trackData) {
        this.currentTrack = trackData.track;
        
        if (this.trackTitle) {
            this.trackTitle.textContent = this.currentTrack.title;
        }
        
        if (this.trackArtist) {
            this.trackArtist.textContent = this.currentTrack.artist;
        }
        
        if (this.trackArtwork) {
            if (this.currentTrack.albumArt) {
                this.trackArtwork.src = this.currentTrack.albumArt.url;
            } else {
                this.trackArtwork.src = 'https://via.placeholder.com/60';
            }
        }
        
        this.updateFavoriteButton({ trackId: this.currentTrack.id });
        this.updateNavigationButtons();
    }

    /**
     * Update shuffle button state
     */
    updateShuffleButton(shuffleData) {
        if (this.shuffleBtn) {
            this.shuffleBtn.classList.toggle('active', shuffleData.shuffle);
        }
    }

    /**
     * Update repeat button state
     */
    updateRepeatButton(repeatData) {
        if (this.repeatBtn) {
            this.repeatBtn.classList.remove('active');
            
            const icon = this.repeatBtn.querySelector('i');
            if (icon) {
                switch (repeatData.repeat) {
                    case 'one':
                        icon.className = 'fas fa-redo-alt';
                        this.repeatBtn.classList.add('active');
                        break;
                    case 'all':
                        icon.className = 'fas fa-redo';
                        this.repeatBtn.classList.add('active');
                        break;
                    default:
                        icon.className = 'fas fa-redo';
                        break;
                }
            }
        }
    }

    /**
     * Update favorite button state
     */
    updateFavoriteButton(favoriteData) {
        if (this.favoriteBtn && this.currentTrack) {
            const isFavorite = this.musicLibrary.isFavorite(this.currentTrack.id);
            const icon = this.favoriteBtn.querySelector('i');
            
            if (icon) {
                icon.className = isFavorite ? 'fas fa-heart' : 'far fa-heart';
            }
            
            this.favoriteBtn.classList.toggle('active', isFavorite);
        }
    }

    /**
     * Update navigation button states
     */
    updateNavigationButtons() {
        const state = this.playbackManager.getState();
        
        if (this.prevBtn) {
            this.prevBtn.disabled = state.currentTrackIndex <= 0 && state.repeat !== 'all';
        }
        
        if (this.nextBtn) {
            this.nextBtn.disabled = state.currentTrackIndex >= state.queue.length - 1 && state.repeat !== 'all';
        }
    }

    /**
     * Update entire UI
     */
    updateUI() {
        const audioState = this.audioEngine.getState();
        const playbackState = this.playbackManager.getState();
        
        this.updatePlayPauseButton(audioState.isPlaying);
        this.updateVolumeDisplay({ volume: audioState.volume, muted: audioState.muted });
        this.updateShuffleButton({ shuffle: playbackState.shuffle });
        this.updateRepeatButton({ repeat: playbackState.repeat });
        
        if (playbackState.currentTrack) {
            this.updateTrackInfo({ track: playbackState.currentTrack });
        }
        
        this.updateNavigationButtons();
    }

    /**
     * Show/hide player bar
     */
    setVisible(visible) {
        if (this.playerBar) {
            this.playerBar.style.display = visible ? 'grid' : 'none';
        }
    }
}
