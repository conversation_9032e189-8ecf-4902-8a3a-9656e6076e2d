// ===== UI MANAGER COMPONENT =====

import { APP_CONFIG, DEFAULT_SETTINGS } from '../utils/constants.js';
import { DOMUtils, StorageUtils } from '../utils/helpers.js';

export class UIManager {
    constructor() {
        // UI State
        this.sidebarCollapsed = false;
        this.rightPanelVisible = true;
        this.currentView = 'home';
        this.currentTab = 'visualizer';
        this.fullscreen = false;
        
        // UI Elements
        this.mainApp = null;
        this.sidebar = null;
        this.rightPanel = null;
        this.contentViews = null;
        this.panelTabs = null;
        this.tabContents = null;
        
        // Navigation
        this.navigationHistory = [];
        this.navigationIndex = -1;
        
        this.initialize();
    }

    /**
     * Initialize UI manager
     */
    initialize() {
        this.setupElements();
        this.setupEventListeners();
        this.loadSettings();
        this.updateLayout();
        
        console.log('UI Manager initialized');
    }

    /**
     * Setup UI elements
     */
    setupElements() {
        this.mainApp = document.getElementById('main-app');
        this.sidebar = document.querySelector('.sidebar');
        this.rightPanel = document.getElementById('right-panel');
        this.contentViews = document.querySelectorAll('.content-view');
        this.panelTabs = document.querySelectorAll('.panel-tab');
        this.tabContents = document.querySelectorAll('.tab-content');
        
        // Navigation elements
        this.backBtn = document.getElementById('back-btn');
        this.forwardBtn = document.getElementById('forward-btn');
        this.sidebarToggle = document.getElementById('sidebar-toggle');
        this.panelToggle = document.getElementById('panel-toggle');
        this.fullscreenBtn = document.getElementById('fullscreen-btn');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Sidebar toggle
        this.sidebarToggle?.addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        // Panel toggle
        this.panelToggle?.addEventListener('click', () => {
            this.toggleRightPanel();
        });
        
        // Fullscreen toggle
        this.fullscreenBtn?.addEventListener('click', () => {
            this.toggleFullscreen();
        });
        
        // Navigation
        this.backBtn?.addEventListener('click', () => {
            this.navigateBack();
        });
        
        this.forwardBtn?.addEventListener('click', () => {
            this.navigateForward();
        });
        
        // Sidebar navigation
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const view = link.dataset.view;
                if (view) {
                    this.navigateToView(view);
                }
            });
        });
        
        // Panel tabs
        this.panelTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                if (tabName) {
                    this.switchPanelTab(tabName);
                }
            });
        });
        
        // Keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        // Window events
        window.addEventListener('resize', DOMUtils.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Fullscreen events
        document.addEventListener('fullscreenchange', () => {
            this.handleFullscreenChange();
        });
    }

    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Don't trigger shortcuts when typing in inputs
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }
            
            switch (e.code) {
                case 'KeyB':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.toggleSidebar();
                    }
                    break;
                case 'KeyP':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.toggleRightPanel();
                    }
                    break;
                case 'F11':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'Escape':
                    if (this.fullscreen) {
                        this.exitFullscreen();
                    }
                    break;
                case 'Slash':
                    if (!e.ctrlKey && !e.metaKey) {
                        e.preventDefault();
                        this.focusSearch();
                    }
                    break;
            }
        });
    }

    /**
     * Navigate to a view
     */
    navigateToView(viewName) {
        if (viewName === this.currentView) return;
        
        // Add to navigation history
        this.addToHistory(viewName);
        
        // Update current view
        this.currentView = viewName;
        
        // Update UI
        this.updateActiveView();
        this.updateNavigation();
        this.updateNavigationButtons();
        
        // Dispatch navigation event
        document.dispatchEvent(new CustomEvent('viewChanged', {
            detail: { view: viewName }
        }));
    }

    /**
     * Add view to navigation history
     */
    addToHistory(viewName) {
        // Remove any forward history
        this.navigationHistory = this.navigationHistory.slice(0, this.navigationIndex + 1);
        
        // Add new view
        this.navigationHistory.push(viewName);
        this.navigationIndex = this.navigationHistory.length - 1;
        
        // Limit history size
        if (this.navigationHistory.length > 50) {
            this.navigationHistory = this.navigationHistory.slice(-50);
            this.navigationIndex = this.navigationHistory.length - 1;
        }
    }

    /**
     * Navigate back
     */
    navigateBack() {
        if (this.navigationIndex > 0) {
            this.navigationIndex--;
            const viewName = this.navigationHistory[this.navigationIndex];
            this.currentView = viewName;
            this.updateActiveView();
            this.updateNavigation();
            this.updateNavigationButtons();
        }
    }

    /**
     * Navigate forward
     */
    navigateForward() {
        if (this.navigationIndex < this.navigationHistory.length - 1) {
            this.navigationIndex++;
            const viewName = this.navigationHistory[this.navigationIndex];
            this.currentView = viewName;
            this.updateActiveView();
            this.updateNavigation();
            this.updateNavigationButtons();
        }
    }

    /**
     * Update active view
     */
    updateActiveView() {
        this.contentViews.forEach(view => {
            const isActive = view.id === `${this.currentView}-view`;
            view.classList.toggle('active', isActive);
        });
    }

    /**
     * Update navigation links
     */
    updateNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            const isActive = link.dataset.view === this.currentView;
            link.classList.toggle('active', isActive);
        });
    }

    /**
     * Update navigation buttons
     */
    updateNavigationButtons() {
        if (this.backBtn) {
            this.backBtn.disabled = this.navigationIndex <= 0;
        }
        
        if (this.forwardBtn) {
            this.forwardBtn.disabled = this.navigationIndex >= this.navigationHistory.length - 1;
        }
    }

    /**
     * Switch panel tab
     */
    switchPanelTab(tabName) {
        this.currentTab = tabName;
        
        // Update tab buttons
        this.panelTabs.forEach(tab => {
            const isActive = tab.dataset.tab === tabName;
            tab.classList.toggle('active', isActive);
        });
        
        // Update tab content
        this.tabContents.forEach(content => {
            const isActive = content.id === `${tabName}-tab`;
            content.classList.toggle('active', isActive);
        });
        
        // Dispatch tab change event
        document.dispatchEvent(new CustomEvent('tabChanged', {
            detail: { tab: tabName }
        }));
    }

    /**
     * Toggle sidebar
     */
    toggleSidebar() {
        this.sidebarCollapsed = !this.sidebarCollapsed;
        this.updateLayout();
        this.saveSettings();
    }

    /**
     * Toggle right panel
     */
    toggleRightPanel() {
        this.rightPanelVisible = !this.rightPanelVisible;
        this.updateLayout();
        this.saveSettings();
    }

    /**
     * Toggle fullscreen
     */
    async toggleFullscreen() {
        if (this.fullscreen) {
            await this.exitFullscreen();
        } else {
            await this.enterFullscreen();
        }
    }

    /**
     * Enter fullscreen mode
     */
    async enterFullscreen() {
        try {
            if (document.documentElement.requestFullscreen) {
                await document.documentElement.requestFullscreen();
            } else if (document.documentElement.webkitRequestFullscreen) {
                await document.documentElement.webkitRequestFullscreen();
            }
        } catch (error) {
            console.error('Failed to enter fullscreen:', error);
        }
    }

    /**
     * Exit fullscreen mode
     */
    async exitFullscreen() {
        try {
            if (document.exitFullscreen) {
                await document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                await document.webkitExitFullscreen();
            }
        } catch (error) {
            console.error('Failed to exit fullscreen:', error);
        }
    }

    /**
     * Handle fullscreen change
     */
    handleFullscreenChange() {
        this.fullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement);
        
        // Update fullscreen button
        if (this.fullscreenBtn) {
            const icon = this.fullscreenBtn.querySelector('i');
            if (icon) {
                icon.className = this.fullscreen ? 'fas fa-compress' : 'fas fa-expand';
            }
        }
        
        // Update body class
        document.body.classList.toggle('fullscreen', this.fullscreen);
    }

    /**
     * Update layout based on current state
     */
    updateLayout() {
        if (this.mainApp) {
            this.mainApp.classList.toggle('sidebar-collapsed', this.sidebarCollapsed);
            this.mainApp.classList.toggle('panel-hidden', !this.rightPanelVisible);
        }
        
        if (this.sidebar) {
            this.sidebar.classList.toggle('collapsed', this.sidebarCollapsed);
        }
        
        if (this.rightPanel) {
            this.rightPanel.style.display = this.rightPanelVisible ? 'flex' : 'none';
        }
        
        // Update toggle button states
        if (this.panelToggle) {
            this.panelToggle.classList.toggle('active', this.rightPanelVisible);
        }
    }

    /**
     * Focus search input
     */
    focusSearch() {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        const width = window.innerWidth;
        
        // Auto-collapse sidebar on small screens
        if (width < 768 && !this.sidebarCollapsed) {
            this.sidebarCollapsed = true;
            this.updateLayout();
        }
        
        // Auto-hide right panel on very small screens
        if (width < 1024 && this.rightPanelVisible) {
            this.rightPanelVisible = false;
            this.updateLayout();
        }
        
        // Dispatch resize event
        document.dispatchEvent(new CustomEvent('uiResize', {
            detail: { width, height: window.innerHeight }
        }));
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notification = DOMUtils.createElement('div', {
            className: `notification notification-${type}`
        });
        
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Add to DOM
        const container = document.querySelector('.notifications-container') || 
                         this.createNotificationsContainer();
        container.appendChild(notification);
        
        // Auto-remove
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);
        
        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn?.addEventListener('click', () => {
            this.removeNotification(notification);
        });
        
        // Animate in
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });
    }

    /**
     * Create notifications container
     */
    createNotificationsContainer() {
        const container = DOMUtils.createElement('div', {
            className: 'notifications-container'
        });
        document.body.appendChild(container);
        return container;
    }

    /**
     * Remove notification
     */
    removeNotification(notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * Show modal
     */
    showModal(content, options = {}) {
        const modal = DOMUtils.createElement('div', {
            className: 'modal-overlay'
        });
        
        const modalContent = DOMUtils.createElement('div', {
            className: 'modal-content'
        });
        
        if (typeof content === 'string') {
            modalContent.innerHTML = content;
        } else {
            modalContent.appendChild(content);
        }
        
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
        
        // Close on overlay click
        if (options.closeOnOverlay !== false) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal);
                }
            });
        }
        
        // Close on escape
        const escapeHandler = (e) => {
            if (e.code === 'Escape') {
                this.closeModal(modal);
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Animate in
        requestAnimationFrame(() => {
            modal.classList.add('show');
        });
        
        return modal;
    }

    /**
     * Close modal
     */
    closeModal(modal) {
        modal.classList.add('hide');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }

    /**
     * Get current UI state
     */
    getState() {
        return {
            sidebarCollapsed: this.sidebarCollapsed,
            rightPanelVisible: this.rightPanelVisible,
            currentView: this.currentView,
            currentTab: this.currentTab,
            fullscreen: this.fullscreen
        };
    }

    /**
     * Save UI settings
     */
    saveSettings() {
        const settings = {
            sidebarCollapsed: this.sidebarCollapsed,
            rightPanelVisible: this.rightPanelVisible,
            currentView: this.currentView,
            currentTab: this.currentTab
        };
        
        StorageUtils.save(APP_CONFIG.storage.settings, settings);
    }

    /**
     * Load UI settings
     */
    loadSettings() {
        const settings = StorageUtils.load(APP_CONFIG.storage.settings, DEFAULT_SETTINGS);
        
        this.sidebarCollapsed = settings.sidebarCollapsed || false;
        this.rightPanelVisible = settings.rightPanelVisible !== false;
        this.currentView = settings.currentView || 'home';
        this.currentTab = settings.currentTab || 'visualizer';
        
        // Apply loaded settings
        this.updateActiveView();
        this.updateNavigation();
        this.switchPanelTab(this.currentTab);
    }
}
