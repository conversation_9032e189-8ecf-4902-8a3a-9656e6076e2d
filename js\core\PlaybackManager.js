// ===== ADVANCED PLAYBACK MANAGER =====

import { APP_CONFIG } from '../utils/constants.js';
import { StorageUtils, MathUtils } from '../utils/helpers.js';

export class PlaybackManager extends EventTarget {
    constructor(audioEngine, musicLibrary) {
        super();
        
        this.audioEngine = audioEngine;
        this.musicLibrary = musicLibrary;
        
        // Playback state
        this.currentTrack = null;
        this.currentTrackIndex = -1;
        this.queue = [];
        this.originalQueue = [];
        this.history = [];
        
        // Playback modes
        this.shuffle = false;
        this.repeat = 'none'; // 'none', 'one', 'all'
        this.crossfade = true;
        this.gapless = true;
        
        // Crossfade state
        this.crossfadeInProgress = false;
        this.nextTrackPreloaded = false;
        
        // Performance tracking
        this.playbackStats = {
            tracksPlayed: 0,
            totalPlaytime: 0,
            skips: 0,
            seeks: 0
        };
        
        this.initialize();
    }

    /**
     * Initialize playback manager
     */
    initialize() {
        this.setupAudioEngineListeners();
        this.loadState();
        
        // Auto-save state periodically
        setInterval(() => {
            this.saveState();
        }, APP_CONFIG.ui.autoSaveInterval);
        
        console.log('Playback Manager initialized');
    }

    /**
     * Setup audio engine event listeners
     */
    setupAudioEngineListeners() {
        this.audioEngine.addEventListener('ended', () => {
            this.handleTrackEnded();
        });

        this.audioEngine.addEventListener('timeupdate', (event) => {
            this.handleTimeUpdate(event.detail);
        });

        this.audioEngine.addEventListener('error', (event) => {
            this.handlePlaybackError(event.detail);
        });
    }

    /**
     * Play a specific track
     */
    async playTrack(track, startTime = 0) {
        try {
            if (!track) {
                throw new Error('No track provided');
            }

            // Add to history if different track
            if (this.currentTrack && this.currentTrack.id !== track.id) {
                this.addToHistory(this.currentTrack);
            }

            this.currentTrack = track;
            
            // Update queue index if track is in queue
            const queueIndex = this.queue.findIndex(t => t.id === track.id);
            if (queueIndex >= 0) {
                this.currentTrackIndex = queueIndex;
            }

            // Load and play track
            await this.audioEngine.loadTrack(track.url);
            
            if (startTime > 0) {
                this.audioEngine.seek(startTime);
            }
            
            await this.audioEngine.play();
            
            // Update play statistics
            this.musicLibrary.updatePlayStats(track.id);
            this.playbackStats.tracksPlayed++;
            
            // Preload next track if gapless playback is enabled
            if (this.gapless) {
                this.preloadNextTrack();
            }
            
            this.dispatchEvent(new CustomEvent('trackChanged', {
                detail: { track, index: this.currentTrackIndex }
            }));
            
            this.saveState();
            
        } catch (error) {
            console.error('Failed to play track:', error);
            this.dispatchEvent(new CustomEvent('playbackError', {
                detail: { error, track }
            }));
        }
    }

    /**
     * Play next track in queue
     */
    async playNext() {
        const nextTrack = this.getNextTrack();
        if (nextTrack) {
            await this.playTrack(nextTrack);
        } else {
            this.dispatchEvent(new CustomEvent('queueEnded'));
        }
    }

    /**
     * Play previous track
     */
    async playPrevious() {
        // If we're more than 3 seconds into the track, restart current track
        if (this.audioEngine.getState().currentTime > 3) {
            this.audioEngine.seek(0);
            return;
        }

        const previousTrack = this.getPreviousTrack();
        if (previousTrack) {
            await this.playTrack(previousTrack);
        }
    }

    /**
     * Get next track based on current mode
     */
    getNextTrack() {
        if (this.queue.length === 0) return null;

        if (this.repeat === 'one') {
            return this.currentTrack;
        }

        let nextIndex = this.currentTrackIndex + 1;

        if (nextIndex >= this.queue.length) {
            if (this.repeat === 'all') {
                nextIndex = 0;
            } else {
                return null;
            }
        }

        return this.queue[nextIndex];
    }

    /**
     * Get previous track
     */
    getPreviousTrack() {
        if (this.queue.length === 0) return null;

        let prevIndex = this.currentTrackIndex - 1;

        if (prevIndex < 0) {
            if (this.repeat === 'all') {
                prevIndex = this.queue.length - 1;
            } else {
                return null;
            }
        }

        return this.queue[prevIndex];
    }

    /**
     * Set playback queue
     */
    setQueue(tracks, startIndex = 0) {
        this.originalQueue = [...tracks];
        this.queue = [...tracks];
        this.currentTrackIndex = Math.max(0, Math.min(startIndex, tracks.length - 1));
        
        if (this.shuffle) {
            this.shuffleQueue();
        }
        
        this.dispatchEvent(new CustomEvent('queueChanged', {
            detail: { queue: this.queue, index: this.currentTrackIndex }
        }));
        
        this.saveState();
    }

    /**
     * Add track to queue
     */
    addToQueue(track, position = 'end') {
        if (position === 'next') {
            const insertIndex = this.currentTrackIndex + 1;
            this.queue.splice(insertIndex, 0, track);
            this.originalQueue.splice(insertIndex, 0, track);
        } else {
            this.queue.push(track);
            this.originalQueue.push(track);
        }
        
        this.dispatchEvent(new CustomEvent('queueChanged', {
            detail: { queue: this.queue, index: this.currentTrackIndex }
        }));
        
        this.saveState();
    }

    /**
     * Remove track from queue
     */
    removeFromQueue(index) {
        if (index >= 0 && index < this.queue.length) {
            const removedTrack = this.queue.splice(index, 1)[0];
            this.originalQueue.splice(this.originalQueue.indexOf(removedTrack), 1);
            
            // Adjust current index if necessary
            if (index < this.currentTrackIndex) {
                this.currentTrackIndex--;
            } else if (index === this.currentTrackIndex) {
                // If we removed the current track, stop playback
                this.audioEngine.stop();
                this.currentTrack = null;
                this.currentTrackIndex = -1;
            }
            
            this.dispatchEvent(new CustomEvent('queueChanged', {
                detail: { queue: this.queue, index: this.currentTrackIndex }
            }));
            
            this.saveState();
        }
    }

    /**
     * Clear queue
     */
    clearQueue() {
        this.queue = [];
        this.originalQueue = [];
        this.currentTrackIndex = -1;
        
        this.dispatchEvent(new CustomEvent('queueChanged', {
            detail: { queue: this.queue, index: this.currentTrackIndex }
        }));
        
        this.saveState();
    }

    /**
     * Toggle shuffle mode
     */
    toggleShuffle() {
        this.shuffle = !this.shuffle;
        
        if (this.shuffle) {
            this.shuffleQueue();
        } else {
            this.restoreOriginalQueue();
        }
        
        this.dispatchEvent(new CustomEvent('shuffleChanged', {
            detail: { shuffle: this.shuffle }
        }));
        
        this.saveState();
        return this.shuffle;
    }

    /**
     * Set repeat mode
     */
    setRepeatMode(mode) {
        const validModes = ['none', 'one', 'all'];
        if (validModes.includes(mode)) {
            this.repeat = mode;
            
            this.dispatchEvent(new CustomEvent('repeatChanged', {
                detail: { repeat: this.repeat }
            }));
            
            this.saveState();
        }
        return this.repeat;
    }

    /**
     * Cycle through repeat modes
     */
    cycleRepeatMode() {
        const modes = ['none', 'one', 'all'];
        const currentIndex = modes.indexOf(this.repeat);
        const nextIndex = (currentIndex + 1) % modes.length;
        return this.setRepeatMode(modes[nextIndex]);
    }

    /**
     * Shuffle the queue
     */
    shuffleQueue() {
        if (this.queue.length <= 1) return;

        // Keep current track at current position
        const currentTrack = this.currentTrack;
        const otherTracks = this.queue.filter(track => track.id !== currentTrack?.id);
        
        // Fisher-Yates shuffle
        for (let i = otherTracks.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [otherTracks[i], otherTracks[j]] = [otherTracks[j], otherTracks[i]];
        }
        
        // Rebuild queue with current track in place
        this.queue = [];
        if (currentTrack) {
            this.queue.push(currentTrack);
            this.currentTrackIndex = 0;
        }
        this.queue.push(...otherTracks);
    }

    /**
     * Restore original queue order
     */
    restoreOriginalQueue() {
        this.queue = [...this.originalQueue];
        
        // Find current track index in original queue
        if (this.currentTrack) {
            this.currentTrackIndex = this.queue.findIndex(track => track.id === this.currentTrack.id);
        }
    }

    /**
     * Add track to history
     */
    addToHistory(track) {
        // Remove if already in history
        this.history = this.history.filter(t => t.id !== track.id);
        
        // Add to beginning
        this.history.unshift(track);
        
        // Limit history size
        if (this.history.length > 50) {
            this.history = this.history.slice(0, 50);
        }
    }

    /**
     * Preload next track for gapless playback
     */
    preloadNextTrack() {
        const nextTrack = this.getNextTrack();
        if (nextTrack && !this.nextTrackPreloaded) {
            // Create a hidden audio element to preload
            const preloadAudio = new Audio();
            preloadAudio.src = nextTrack.url;
            preloadAudio.preload = 'auto';
            this.nextTrackPreloaded = true;
            
            // Clean up after a while
            setTimeout(() => {
                preloadAudio.src = '';
                this.nextTrackPreloaded = false;
            }, 30000);
        }
    }

    /**
     * Handle track ended
     */
    handleTrackEnded() {
        this.playbackStats.totalPlaytime += this.currentTrack?.duration || 0;
        
        if (this.crossfade && this.getNextTrack()) {
            this.performCrossfade();
        } else {
            this.playNext();
        }
    }

    /**
     * Perform crossfade to next track
     */
    async performCrossfade() {
        if (this.crossfadeInProgress) return;
        
        const nextTrack = this.getNextTrack();
        if (!nextTrack) {
            this.playNext();
            return;
        }
        
        this.crossfadeInProgress = true;
        
        try {
            // Create second audio element for crossfade
            const nextAudio = new Audio();
            nextAudio.src = nextTrack.url;
            nextAudio.volume = 0;
            
            // Start playing next track
            await nextAudio.play();
            
            // Crossfade duration
            const fadeTime = APP_CONFIG.audio.crossfadeTime;
            const steps = 50;
            const stepTime = fadeTime / steps;
            
            // Perform crossfade
            for (let i = 0; i <= steps; i++) {
                const progress = i / steps;
                const currentVolume = 1 - progress;
                const nextVolume = progress;
                
                this.audioEngine.setVolume(currentVolume * this.audioEngine.volume);
                nextAudio.volume = nextVolume * this.audioEngine.volume;
                
                await new Promise(resolve => setTimeout(resolve, stepTime));
            }
            
            // Switch to next track
            this.audioEngine.stop();
            await this.playTrack(nextTrack);
            
            // Clean up
            nextAudio.pause();
            nextAudio.src = '';
            
        } catch (error) {
            console.error('Crossfade failed:', error);
            this.playNext();
        } finally {
            this.crossfadeInProgress = false;
        }
    }

    /**
     * Handle time update
     */
    handleTimeUpdate(timeData) {
        // Update total playtime
        this.playbackStats.totalPlaytime += 0.1; // Approximate
        
        // Dispatch time update event
        this.dispatchEvent(new CustomEvent('timeUpdate', {
            detail: timeData
        }));
    }

    /**
     * Handle playback error
     */
    handlePlaybackError(errorData) {
        console.error('Playback error:', errorData);
        
        // Try to play next track
        setTimeout(() => {
            this.playNext();
        }, 1000);
    }

    /**
     * Get current playback state
     */
    getState() {
        return {
            currentTrack: this.currentTrack,
            currentTrackIndex: this.currentTrackIndex,
            queue: this.queue,
            shuffle: this.shuffle,
            repeat: this.repeat,
            crossfade: this.crossfade,
            gapless: this.gapless,
            history: this.history,
            stats: this.playbackStats
        };
    }

    /**
     * Save state to storage
     */
    saveState() {
        const state = {
            currentTrackId: this.currentTrack?.id,
            currentTrackIndex: this.currentTrackIndex,
            queueIds: this.queue.map(track => track.id),
            originalQueueIds: this.originalQueue.map(track => track.id),
            shuffle: this.shuffle,
            repeat: this.repeat,
            crossfade: this.crossfade,
            gapless: this.gapless,
            stats: this.playbackStats
        };
        
        StorageUtils.save(APP_CONFIG.storage.queue, state);
    }

    /**
     * Load state from storage
     */
    loadState() {
        const state = StorageUtils.load(APP_CONFIG.storage.queue);
        
        if (state) {
            this.shuffle = state.shuffle || false;
            this.repeat = state.repeat || 'none';
            this.crossfade = state.crossfade !== false;
            this.gapless = state.gapless !== false;
            this.playbackStats = state.stats || this.playbackStats;
            
            // Restore queue if tracks are available
            if (state.queueIds && state.queueIds.length > 0) {
                const tracks = state.queueIds
                    .map(id => this.musicLibrary.getTrack(id))
                    .filter(Boolean);
                
                if (tracks.length > 0) {
                    this.queue = tracks;
                    this.currentTrackIndex = state.currentTrackIndex || 0;
                    
                    if (state.currentTrackId) {
                        this.currentTrack = this.musicLibrary.getTrack(state.currentTrackId);
                    }
                }
            }
        }
    }
}
