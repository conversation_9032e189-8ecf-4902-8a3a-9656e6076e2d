// ===== ADVANCED MUSIC LIBRARY MANAGER =====

import { APP_CONFIG, SUPPORTED_FORMATS, SUPPORTED_IMAGE_FORMATS } from '../utils/constants.js';
import { FileUtils, StorageUtils, TimeUtils } from '../utils/helpers.js';

export class MusicLibrary extends EventTarget {
    constructor() {
        super();
        
        // Library data
        this.tracks = new Map();
        this.artists = new Map();
        this.albums = new Map();
        this.playlists = new Map();
        this.favorites = new Set();
        this.recentTracks = [];
        
        // Metadata cache
        this.albumArtCache = new Map();
        this.metadataCache = new Map();
        
        // Search and filtering
        this.searchIndex = new Map();
        this.filters = {
            artist: null,
            album: null,
            genre: null,
            year: null
        };
        
        // Settings
        this.sortBy = 'title';
        this.sortOrder = 'asc';
        this.groupBy = 'none';
        
        this.initialize();
    }

    /**
     * Initialize the music library
     */
    async initialize() {
        try {
            await this.loadFromStorage();
            this.buildSearchIndex();
            this.dispatchEvent(new CustomEvent('initialized'));
            console.log('Music Library initialized');
        } catch (error) {
            console.error('Failed to initialize Music Library:', error);
        }
    }

    /**
     * Import music files from file input
     */
    async importFiles(files) {
        const importedTracks = [];
        const importedAlbumArt = [];
        
        // Separate audio and image files
        const audioFiles = files.filter(file => FileUtils.isAudioFile(file));
        const imageFiles = files.filter(file => FileUtils.isImageFile(file));
        
        // Process image files first for album art
        for (const imageFile of imageFiles) {
            await this.processAlbumArt(imageFile);
        }
        
        // Process audio files
        for (const audioFile of audioFiles) {
            try {
                const track = await this.processAudioFile(audioFile);
                if (track) {
                    importedTracks.push(track);
                }
            } catch (error) {
                console.error(`Failed to process ${audioFile.name}:`, error);
            }
        }
        
        // Update library structure
        this.updateLibraryStructure();
        this.buildSearchIndex();
        await this.saveToStorage();
        
        this.dispatchEvent(new CustomEvent('imported', {
            detail: {
                tracksCount: importedTracks.length,
                albumArtCount: importedAlbumArt.length,
                tracks: importedTracks
            }
        }));
        
        return importedTracks;
    }

    /**
     * Process audio file and extract metadata
     */
    async processAudioFile(file) {
        const trackId = this.generateTrackId(file);
        
        // Extract basic metadata from file path/name
        const metadata = FileUtils.extractMetadataFromPath(file.webkitRelativePath || file.name);
        
        // Create track object
        const track = {
            id: trackId,
            file: file,
            url: FileUtils.createObjectURL(file),
            title: metadata.title,
            artist: metadata.artist,
            album: metadata.album,
            genre: 'Unknown',
            year: null,
            duration: 0,
            trackNumber: null,
            diskNumber: null,
            size: file.size,
            format: file.type,
            path: file.webkitRelativePath || file.name,
            dateAdded: Date.now(),
            playCount: 0,
            lastPlayed: null,
            albumArt: null
        };

        // Try to find album art for this track
        track.albumArt = this.findAlbumArt(track);
        
        // Try to extract additional metadata from file
        try {
            const additionalMetadata = await this.extractFileMetadata(file);
            Object.assign(track, additionalMetadata);
        } catch (error) {
            console.warn(`Could not extract metadata from ${file.name}:`, error);
        }
        
        // Add to library
        this.tracks.set(trackId, track);
        
        return track;
    }

    /**
     * Process album art image file
     */
    async processAlbumArt(imageFile) {
        const path = imageFile.webkitRelativePath || imageFile.name;
        const pathParts = path.split('/');
        
        // Try to determine which artist/album this belongs to
        let artistName = 'Unknown Artist';
        
        if (pathParts.includes('Album Art') || pathParts.includes('Artwork')) {
            // Find artist name from path structure
            const artistIndex = pathParts.findIndex(part => part === 'ARTISTS');
            if (artistIndex >= 0 && artistIndex + 1 < pathParts.length) {
                artistName = pathParts[artistIndex + 1];
            }
        }
        
        const artworkData = {
            file: imageFile,
            url: FileUtils.createObjectURL(imageFile),
            artist: artistName,
            path: path
        };
        
        this.albumArtCache.set(artistName, artworkData);
        return artworkData;
    }

    /**
     * Find album art for a track
     */
    findAlbumArt(track) {
        // First try exact artist match
        if (this.albumArtCache.has(track.artist)) {
            return this.albumArtCache.get(track.artist);
        }
        
        // Try case-insensitive match
        for (const [artist, artwork] of this.albumArtCache.entries()) {
            if (artist.toLowerCase() === track.artist.toLowerCase()) {
                return artwork;
            }
        }
        
        return null;
    }

    /**
     * Extract metadata from audio file using Web Audio API
     */
    async extractFileMetadata(file) {
        return new Promise((resolve) => {
            const audio = new Audio();
            const url = FileUtils.createObjectURL(file);
            
            audio.addEventListener('loadedmetadata', () => {
                const metadata = {
                    duration: audio.duration || 0
                };
                
                URL.revokeObjectURL(url);
                resolve(metadata);
            });
            
            audio.addEventListener('error', () => {
                URL.revokeObjectURL(url);
                resolve({ duration: 0 });
            });
            
            audio.src = url;
        });
    }

    /**
     * Generate unique track ID
     */
    generateTrackId(file) {
        const path = file.webkitRelativePath || file.name;
        const size = file.size;
        const modified = file.lastModified || Date.now();
        return btoa(`${path}-${size}-${modified}`).replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * Update library structure (artists, albums)
     */
    updateLibraryStructure() {
        this.artists.clear();
        this.albums.clear();
        
        for (const track of this.tracks.values()) {
            // Update artists
            if (!this.artists.has(track.artist)) {
                this.artists.set(track.artist, {
                    name: track.artist,
                    tracks: [],
                    albums: new Set(),
                    albumArt: track.albumArt
                });
            }
            this.artists.get(track.artist).tracks.push(track.id);
            this.artists.get(track.artist).albums.add(track.album);
            
            // Update albums
            const albumKey = `${track.artist}::${track.album}`;
            if (!this.albums.has(albumKey)) {
                this.albums.set(albumKey, {
                    title: track.album,
                    artist: track.artist,
                    tracks: [],
                    year: track.year,
                    genre: track.genre,
                    albumArt: track.albumArt
                });
            }
            this.albums.get(albumKey).tracks.push(track.id);
        }
    }

    /**
     * Build search index for fast searching
     */
    buildSearchIndex() {
        this.searchIndex.clear();
        
        for (const track of this.tracks.values()) {
            const searchTerms = [
                track.title,
                track.artist,
                track.album,
                track.genre
            ].filter(Boolean).join(' ').toLowerCase();
            
            // Create n-grams for partial matching
            const words = searchTerms.split(' ');
            words.forEach(word => {
                for (let i = 0; i < word.length - 1; i++) {
                    const ngram = word.substring(i, i + 3);
                    if (!this.searchIndex.has(ngram)) {
                        this.searchIndex.set(ngram, new Set());
                    }
                    this.searchIndex.get(ngram).add(track.id);
                }
            });
        }
    }

    /**
     * Search tracks
     */
    search(query) {
        if (!query || query.length < 2) {
            return Array.from(this.tracks.values());
        }
        
        const queryLower = query.toLowerCase();
        const matchingTrackIds = new Set();
        
        // Direct text matching
        for (const track of this.tracks.values()) {
            const searchText = `${track.title} ${track.artist} ${track.album} ${track.genre}`.toLowerCase();
            if (searchText.includes(queryLower)) {
                matchingTrackIds.add(track.id);
            }
        }
        
        // N-gram matching for partial matches
        const queryWords = queryLower.split(' ');
        queryWords.forEach(word => {
            for (let i = 0; i <= word.length - 3; i++) {
                const ngram = word.substring(i, i + 3);
                if (this.searchIndex.has(ngram)) {
                    this.searchIndex.get(ngram).forEach(trackId => {
                        matchingTrackIds.add(trackId);
                    });
                }
            }
        });
        
        return Array.from(matchingTrackIds).map(id => this.tracks.get(id)).filter(Boolean);
    }

    /**
     * Get tracks with filtering and sorting
     */
    getTracks(options = {}) {
        let tracks = Array.from(this.tracks.values());
        
        // Apply filters
        if (options.artist) {
            tracks = tracks.filter(track => track.artist === options.artist);
        }
        if (options.album) {
            tracks = tracks.filter(track => track.album === options.album);
        }
        if (options.genre) {
            tracks = tracks.filter(track => track.genre === options.genre);
        }
        if (options.year) {
            tracks = tracks.filter(track => track.year === options.year);
        }
        
        // Apply sorting
        const sortBy = options.sortBy || this.sortBy;
        const sortOrder = options.sortOrder || this.sortOrder;
        
        tracks.sort((a, b) => {
            let aVal = a[sortBy] || '';
            let bVal = b[sortBy] || '';
            
            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }
            
            let result = 0;
            if (aVal < bVal) result = -1;
            else if (aVal > bVal) result = 1;
            
            return sortOrder === 'desc' ? -result : result;
        });
        
        return tracks;
    }

    /**
     * Get track by ID
     */
    getTrack(trackId) {
        return this.tracks.get(trackId);
    }

    /**
     * Get all artists
     */
    getArtists() {
        return Array.from(this.artists.values()).sort((a, b) => 
            a.name.localeCompare(b.name)
        );
    }

    /**
     * Get all albums
     */
    getAlbums() {
        return Array.from(this.albums.values()).sort((a, b) => 
            a.title.localeCompare(b.title)
        );
    }

    /**
     * Add track to favorites
     */
    addToFavorites(trackId) {
        this.favorites.add(trackId);
        this.saveToStorage();
        this.dispatchEvent(new CustomEvent('favoritesChanged', { detail: { trackId, added: true } }));
    }

    /**
     * Remove track from favorites
     */
    removeFromFavorites(trackId) {
        this.favorites.delete(trackId);
        this.saveToStorage();
        this.dispatchEvent(new CustomEvent('favoritesChanged', { detail: { trackId, added: false } }));
    }

    /**
     * Check if track is favorite
     */
    isFavorite(trackId) {
        return this.favorites.has(trackId);
    }

    /**
     * Get favorite tracks
     */
    getFavorites() {
        return Array.from(this.favorites).map(id => this.tracks.get(id)).filter(Boolean);
    }

    /**
     * Add track to recent tracks
     */
    addToRecent(trackId) {
        // Remove if already exists
        this.recentTracks = this.recentTracks.filter(id => id !== trackId);
        
        // Add to beginning
        this.recentTracks.unshift(trackId);
        
        // Limit size
        if (this.recentTracks.length > APP_CONFIG.ui.maxRecentTracks) {
            this.recentTracks = this.recentTracks.slice(0, APP_CONFIG.ui.maxRecentTracks);
        }
        
        this.saveToStorage();
    }

    /**
     * Get recent tracks
     */
    getRecentTracks() {
        return this.recentTracks.map(id => this.tracks.get(id)).filter(Boolean);
    }

    /**
     * Update track play count and last played
     */
    updatePlayStats(trackId) {
        const track = this.tracks.get(trackId);
        if (track) {
            track.playCount = (track.playCount || 0) + 1;
            track.lastPlayed = Date.now();
            this.addToRecent(trackId);
            this.saveToStorage();
        }
    }

    /**
     * Save library to localStorage
     */
    async saveToStorage() {
        try {
            // Convert Maps and Sets to arrays for storage
            const libraryData = {
                tracks: Array.from(this.tracks.entries()),
                favorites: Array.from(this.favorites),
                recentTracks: this.recentTracks,
                settings: {
                    sortBy: this.sortBy,
                    sortOrder: this.sortOrder,
                    groupBy: this.groupBy
                }
            };
            
            StorageUtils.save(APP_CONFIG.storage.library, libraryData);
        } catch (error) {
            console.error('Failed to save library to storage:', error);
        }
    }

    /**
     * Load library from localStorage
     */
    async loadFromStorage() {
        try {
            const libraryData = StorageUtils.load(APP_CONFIG.storage.library);
            
            if (libraryData) {
                // Restore tracks
                if (libraryData.tracks) {
                    this.tracks = new Map(libraryData.tracks);
                }
                
                // Restore favorites
                if (libraryData.favorites) {
                    this.favorites = new Set(libraryData.favorites);
                }
                
                // Restore recent tracks
                if (libraryData.recentTracks) {
                    this.recentTracks = libraryData.recentTracks;
                }
                
                // Restore settings
                if (libraryData.settings) {
                    Object.assign(this, libraryData.settings);
                }
                
                // Update library structure
                this.updateLibraryStructure();
            }
        } catch (error) {
            console.error('Failed to load library from storage:', error);
        }
    }

    /**
     * Clear entire library
     */
    clear() {
        this.tracks.clear();
        this.artists.clear();
        this.albums.clear();
        this.favorites.clear();
        this.recentTracks = [];
        this.albumArtCache.clear();
        this.searchIndex.clear();
        
        // Cleanup object URLs
        FileUtils.cleanupObjectURLs();
        
        this.saveToStorage();
        this.dispatchEvent(new CustomEvent('cleared'));
    }

    /**
     * Get library statistics
     */
    getStats() {
        const totalDuration = Array.from(this.tracks.values())
            .reduce((sum, track) => sum + (track.duration || 0), 0);
        
        return {
            totalTracks: this.tracks.size,
            totalArtists: this.artists.size,
            totalAlbums: this.albums.size,
            totalDuration: totalDuration,
            totalSize: Array.from(this.tracks.values())
                .reduce((sum, track) => sum + track.size, 0),
            favoritesCount: this.favorites.size,
            formattedDuration: TimeUtils.formatTime(totalDuration)
        };
    }
}
