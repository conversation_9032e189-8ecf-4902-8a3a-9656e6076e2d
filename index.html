<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nexus Music Player</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body>
    <div id="app" class="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-content">
                <div class="loading-logo">
                    <i class="fas fa-music"></i>
                    <span>NEXUS</span>
                </div>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p class="loading-text">Initializing audio engine...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <div class="logo">
                        <i class="fas fa-music"></i>
                        <span>NEXUS</span>
                    </div>
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <h3>Library</h3>
                        <ul>
                            <li><a href="#" data-view="home" class="nav-link active"><i class="fas fa-home"></i> Home</a></li>
                            <li><a href="#" data-view="library" class="nav-link"><i class="fas fa-music"></i> Your Music</a></li>
                            <li><a href="#" data-view="playlists" class="nav-link"><i class="fas fa-list"></i> Playlists</a></li>
                            <li><a href="#" data-view="favorites" class="nav-link"><i class="fas fa-heart"></i> Favorites</a></li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>Discover</h3>
                        <ul>
                            <li><a href="#" data-view="genres" class="nav-link"><i class="fas fa-tags"></i> Genres</a></li>
                            <li><a href="#" data-view="artists" class="nav-link"><i class="fas fa-user-music"></i> Artists</a></li>
                            <li><a href="#" data-view="albums" class="nav-link"><i class="fas fa-compact-disc"></i> Albums</a></li>
                        </ul>
                    </div>
                </nav>

                <div class="sidebar-footer">
                    <button class="import-btn" id="import-music">
                        <i class="fas fa-plus"></i>
                        <span>Import Music</span>
                    </button>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Top Bar -->
                <header class="top-bar">
                    <div class="top-bar-left">
                        <div class="navigation-controls">
                            <button class="nav-btn" id="back-btn" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="nav-btn" id="forward-btn" disabled>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div class="search-container">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="Search for songs, artists, albums..." id="search-input">
                            <button class="search-clear" id="search-clear">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="top-bar-right">
                        <div class="user-controls">
                            <button class="control-btn" id="settings-btn" title="Settings">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button class="control-btn" id="fullscreen-btn" title="Fullscreen">
                                <i class="fas fa-expand"></i>
                            </button>
                            <div class="user-profile">
                                <img src="https://via.placeholder.com/32" alt="User" class="user-avatar">
                                <span class="user-name">Music Lover</span>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Content Views -->
                <div class="content-container">
                    <div id="home-view" class="content-view active">
                        <div class="welcome-section">
                            <h1>Welcome back!</h1>
                            <p>Ready to discover your next favorite song?</p>
                        </div>
                        
                        <div class="quick-actions">
                            <button class="quick-action-btn" id="quick-import">
                                <i class="fas fa-folder-open"></i>
                                <span>Import Your Music</span>
                                <p>Add music from your computer</p>
                            </button>
                            <button class="quick-action-btn" id="quick-playlist">
                                <i class="fas fa-plus-circle"></i>
                                <span>Create Playlist</span>
                                <p>Organize your favorite tracks</p>
                            </button>
                            <button class="quick-action-btn" id="quick-shuffle">
                                <i class="fas fa-random"></i>
                                <span>Shuffle All</span>
                                <p>Play all your music randomly</p>
                            </button>
                        </div>

                        <div class="recent-section">
                            <h2>Recently Played</h2>
                            <div class="recent-tracks" id="recent-tracks">
                                <!-- Recent tracks will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div id="library-view" class="content-view">
                        <div class="view-header">
                            <h1>Your Music Library</h1>
                            <div class="view-controls">
                                <div class="sort-controls">
                                    <select id="sort-select" class="sort-select">
                                        <option value="title">Sort by Title</option>
                                        <option value="artist">Sort by Artist</option>
                                        <option value="album">Sort by Album</option>
                                        <option value="date">Sort by Date Added</option>
                                    </select>
                                </div>
                                <div class="view-toggle">
                                    <button class="view-toggle-btn active" data-view="grid">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button class="view-toggle-btn" data-view="list">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="music-grid" id="music-grid">
                            <!-- Music tracks will be populated here -->
                        </div>
                    </div>

                    <!-- Other views will be added dynamically -->
                </div>
            </main>

            <!-- Right Panel (Visualizer/Queue) -->
            <aside class="right-panel" id="right-panel">
                <div class="panel-tabs">
                    <button class="panel-tab active" data-tab="visualizer">
                        <i class="fas fa-wave-square"></i>
                        <span>Visualizer</span>
                    </button>
                    <button class="panel-tab" data-tab="queue">
                        <i class="fas fa-list-ol"></i>
                        <span>Queue</span>
                    </button>
                    <button class="panel-tab" data-tab="lyrics">
                        <i class="fas fa-align-left"></i>
                        <span>Lyrics</span>
                    </button>
                </div>

                <div class="panel-content">
                    <div id="visualizer-tab" class="tab-content active">
                        <div class="visualizer-container">
                            <canvas id="visualizer-canvas" class="visualizer-canvas"></canvas>
                            <div class="visualizer-controls">
                                <button class="viz-mode-btn active" data-mode="bars">Bars</button>
                                <button class="viz-mode-btn" data-mode="wave">Wave</button>
                                <button class="viz-mode-btn" data-mode="circle">Circle</button>
                                <button class="viz-mode-btn" data-mode="particles">Particles</button>
                            </div>
                        </div>
                    </div>

                    <div id="queue-tab" class="tab-content">
                        <div class="queue-header">
                            <h3>Up Next</h3>
                            <button class="clear-queue-btn" id="clear-queue">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="queue-list" id="queue-list">
                            <!-- Queue items will be populated here -->
                        </div>
                    </div>

                    <div id="lyrics-tab" class="tab-content">
                        <div class="lyrics-container" id="lyrics-container">
                            <p class="no-lyrics">No lyrics available</p>
                        </div>
                    </div>
                </div>
            </aside>
        </div>

        <!-- Player Bar -->
        <div class="player-bar" id="player-bar">
            <div class="player-info">
                <div class="track-artwork">
                    <img id="current-artwork" src="https://via.placeholder.com/60" alt="Track artwork">
                    <div class="artwork-overlay">
                        <button class="artwork-btn" id="expand-player">
                            <i class="fas fa-expand-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="track-details">
                    <div class="track-title" id="current-title">Select a track</div>
                    <div class="track-artist" id="current-artist">No artist</div>
                </div>
                <button class="favorite-btn" id="favorite-btn">
                    <i class="far fa-heart"></i>
                </button>
            </div>

            <div class="player-controls">
                <div class="control-buttons">
                    <button class="control-btn" id="shuffle-btn" title="Shuffle">
                        <i class="fas fa-random"></i>
                    </button>
                    <button class="control-btn" id="prev-btn" title="Previous">
                        <i class="fas fa-step-backward"></i>
                    </button>
                    <button class="control-btn play-pause-btn" id="play-pause-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="control-btn" id="next-btn" title="Next">
                        <i class="fas fa-step-forward"></i>
                    </button>
                    <button class="control-btn" id="repeat-btn" title="Repeat">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                <div class="progress-container">
                    <span class="time-display" id="current-time">0:00</span>
                    <div class="progress-bar" id="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                        <div class="progress-handle" id="progress-handle"></div>
                    </div>
                    <span class="time-display" id="total-time">0:00</span>
                </div>
            </div>

            <div class="player-extras">
                <button class="control-btn" id="queue-btn" title="Queue">
                    <i class="fas fa-list"></i>
                </button>
                <div class="volume-container">
                    <button class="control-btn" id="volume-btn" title="Volume">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <div class="volume-slider" id="volume-slider">
                        <div class="volume-fill" id="volume-fill"></div>
                        <div class="volume-handle" id="volume-handle"></div>
                    </div>
                </div>
                <button class="control-btn" id="equalizer-btn" title="Equalizer">
                    <i class="fas fa-sliders-h"></i>
                </button>
                <button class="control-btn" id="panel-toggle" title="Toggle Panel">
                    <i class="fas fa-sidebar"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Hidden file input for music import -->
    <input type="file" id="music-file-input" multiple accept="audio/*" style="display: none;">

    <!-- Audio element -->
    <audio id="audio-player" crossorigin="anonymous"></audio>

    <!-- Scripts -->
    <script type="module" src="js/app.js"></script>

    <!-- Fallback script for basic functionality -->
    <script>
        // Basic fallback functionality if modules fail
        document.addEventListener('DOMContentLoaded', function() {
            const importBtn = document.getElementById('import-music');
            const quickImport = document.getElementById('quick-import');
            const fileInput = document.getElementById('music-file-input');

            function triggerImport() {
                fileInput.click();
            }

            if (importBtn) importBtn.addEventListener('click', triggerImport);
            if (quickImport) quickImport.addEventListener('click', triggerImport);

            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    const files = Array.from(e.target.files);
                    console.log('Selected files:', files);
                    alert(`Selected ${files.length} files. Full functionality requires a web server.`);
                });
            }

            // Hide loading screen after 3 seconds if app doesn't load
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                const mainApp = document.getElementById('main-app');

                if (loadingScreen && loadingScreen.style.display !== 'none') {
                    loadingScreen.style.display = 'none';
                    if (mainApp) {
                        mainApp.classList.remove('hidden');
                    }
                }
            }, 3000);
        });
    </script>
</body>
</html>
