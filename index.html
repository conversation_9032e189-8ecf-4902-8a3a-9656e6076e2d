<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WSOD - Music Player</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <span class="logo-text">WSOD</span>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item active">
                    <i class="fas fa-home"></i>
                </div>
            </nav>
            
            <div class="user-avatar">
                <img src="https://via.placeholder.com/40x40/4A90E2/FFFFFF?text=U" alt="User">
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-tabs">
                    <button class="tab-btn active">New Music</button>
                    <button class="tab-btn">Friends</button>
                    <button class="tab-btn">Radio</button>
                </div>
                
                <div class="header-actions">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search for artists...">
                    </div>
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                    </button>
                </div>
            </header>

            <!-- Hero Section -->
            <div class="hero-section">
                <div class="hero-card">
                    <div class="hero-content">
                        <div class="hero-info">
                            <span class="hero-subtitle">The Internet</span>
                            <h1 class="hero-title">Come Over</h1>
                        </div>
                        <button class="play-btn-hero">
                            <i class="fas fa-pause"></i>
                        </button>
                    </div>
                    <div class="hero-bg"></div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Popular Section -->
                <div class="section">
                    <h2 class="section-title">Popular</h2>
                    <div class="track-list" id="popular-tracks">
                        <!-- Tracks will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Fans Also Like Section -->
                <div class="section">
                    <h2 class="section-title">Fans also like</h2>
                    <div class="artist-list" id="similar-artists">
                        <!-- Artists will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div class="right-sidebar">
            <!-- Search Tags -->
            <div class="widget">
                <h3 class="widget-title">Search Tags</h3>
                <div class="tag-list">
                    <span class="tag">Hip Hop <i class="fas fa-times"></i></span>
                    <span class="tag">R&B <i class="fas fa-times"></i></span>
                    <span class="tag">Jazz <i class="fas fa-times"></i></span>
                    <span class="tag">Rock <i class="fas fa-times"></i></span>
                    <span class="tag">Pop <i class="fas fa-times"></i></span>
                    <span class="tag">Electronic <i class="fas fa-times"></i></span>
                </div>
            </div>

            <!-- New Podcasts -->
            <div class="widget">
                <h3 class="widget-title">New Podcasts</h3>
                <div class="podcast-grid">
                    <div class="podcast-card blue"></div>
                    <div class="podcast-card purple"></div>
                    <div class="podcast-card teal"></div>
                </div>
                
                <div class="featured-podcast">
                    <div class="podcast-info">
                        <img src="https://via.placeholder.com/50x50/FF6B6B/FFFFFF?text=KD" alt="Podcast">
                        <div class="podcast-details">
                            <h4>KING'S DISEASE</h4>
                            <div class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <span>4.9</span>
                            </div>
                            <p>King's Disease is the thirteenth studio album by American rapper Nas. It was released on August 21, 2020.</p>
                        </div>
                    </div>
                    <button class="play-btn-small">
                        <i class="fas fa-play"></i>
                        Play on My Phone
                    </button>
                    <button class="remind-btn">
                        <i class="fas fa-bell"></i>
                        Remind me later
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Player -->
    <div class="bottom-player" id="bottom-player">
        <div class="player-track-info">
            <img src="https://via.placeholder.com/50x50/4A90E2/FFFFFF?text=♪" alt="Track" id="player-artwork">
            <div class="track-details">
                <div class="track-name" id="player-track-name">Select a track</div>
                <div class="track-artist" id="player-track-artist">No artist</div>
            </div>
        </div>

        <div class="player-controls">
            <button class="control-btn" id="shuffle-btn">
                <i class="fas fa-random"></i>
            </button>
            <button class="control-btn" id="prev-btn">
                <i class="fas fa-step-backward"></i>
            </button>
            <button class="control-btn play-btn" id="play-pause-btn">
                <i class="fas fa-play"></i>
            </button>
            <button class="control-btn" id="next-btn">
                <i class="fas fa-step-forward"></i>
            </button>
            <button class="control-btn" id="repeat-btn">
                <i class="fas fa-redo"></i>
            </button>
        </div>

        <div class="player-extras">
            <button class="control-btn" id="queue-btn">
                <i class="fas fa-list"></i>
            </button>
            <button class="control-btn" id="volume-btn">
                <i class="fas fa-volume-up"></i>
            </button>
            <div class="volume-slider">
                <input type="range" id="volume-range" min="0" max="100" value="100">
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="progress-container">
            <span class="time-current" id="current-time">0:00</span>
            <div class="progress-bar" id="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
                <div class="progress-handle" id="progress-handle"></div>
            </div>
            <span class="time-total" id="total-time">0:00</span>
        </div>
    </div>

    <!-- Audio Element -->
    <audio id="audio-player" preload="metadata"></audio>

    <script src="scripts/player.js"></script>
</body>
</html>
