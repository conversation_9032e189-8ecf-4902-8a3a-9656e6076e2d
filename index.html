<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WSOD - Music Player</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <span class="logo-text">WSOD</span>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item active">
                    <i class="fas fa-home"></i>
                </div>
            </nav>
            
            <div class="user-avatar">
                <img src="https://via.placeholder.com/40x40/4A90E2/FFFFFF?text=U" alt="User">
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-tabs">
                    <button class="tab-btn active">New Music</button>
                    <button class="tab-btn">Friends</button>
                    <button class="tab-btn">Radio</button>
                </div>
                
                <div class="header-actions">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search for artists...">
                    </div>
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                    </button>
                </div>
            </header>

            <!-- Hero Section -->
            <div class="hero-section">
                <div class="hero-card">
                    <div class="hero-content">
                        <div class="hero-info">
                            <span class="hero-subtitle">The Internet</span>
                            <h1 class="hero-title">Come Over</h1>
                        </div>
                        <button class="play-btn-hero">
                            <i class="fas fa-pause"></i>
                        </button>
                    </div>
                    <div class="hero-bg"></div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Popular Section -->
                <div class="section">
                    <h2 class="section-title">Popular</h2>
                    <div class="track-list" id="popular-tracks">
                        <!-- Tracks will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Fans Also Like Section -->
                <div class="section">
                    <h2 class="section-title">Fans also like</h2>
                    <div class="artist-list" id="similar-artists">
                        <!-- Artists will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div class="right-sidebar">
            <!-- Search -->
            <div class="widget">
                <h3 class="widget-title">Search</h3>
                <div class="search-input">
                    <input type="text" placeholder="Search music..." id="searchInput">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <!-- Library Stats -->
            <div class="widget">
                <h3 class="widget-title">Library Stats</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalTracks">0</div>
                        <div class="stat-label">Tracks</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalArtists">0</div>
                        <div class="stat-label">Artists</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalAlbums">0</div>
                        <div class="stat-label">Albums</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Player -->
    <div class="bottom-player" id="bottom-player">
        <div class="player-track-info">
            <img src="https://via.placeholder.com/50x50/4A90E2/FFFFFF?text=♪" alt="Track" id="player-artwork">
            <div class="track-details">
                <div class="track-name" id="player-track-name">Select a track</div>
                <div class="track-artist" id="player-track-artist">No artist</div>
            </div>
        </div>

        <div class="player-controls">
            <button class="control-btn" id="shuffle-btn">
                <i class="fas fa-random"></i>
            </button>
            <button class="control-btn" id="prev-btn">
                <i class="fas fa-step-backward"></i>
            </button>
            <button class="control-btn play-btn" id="play-pause-btn">
                <i class="fas fa-play"></i>
            </button>
            <button class="control-btn" id="next-btn">
                <i class="fas fa-step-forward"></i>
            </button>
            <button class="control-btn" id="repeat-btn">
                <i class="fas fa-redo"></i>
            </button>
        </div>

        <div class="player-extras">
            <button class="control-btn" id="queue-btn">
                <i class="fas fa-list"></i>
            </button>
            <button class="control-btn" id="volume-btn">
                <i class="fas fa-volume-up"></i>
            </button>
            <div class="volume-slider">
                <input type="range" id="volume-range" min="0" max="100" value="100">
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="progress-container">
            <span class="time-current" id="current-time">0:00</span>
            <div class="progress-bar" id="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
                <div class="progress-handle" id="progress-handle"></div>
            </div>
            <span class="time-total" id="total-time">0:00</span>
        </div>
    </div>

    <!-- Audio Element -->
    <audio id="audio-player" preload="metadata"></audio>

    <script src="scripts/player.js"></script>
</body>
</html>
