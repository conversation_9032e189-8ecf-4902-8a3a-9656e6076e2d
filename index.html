<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nexus Music Player</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body>
    <div id="app" class="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-content">
                <div class="loading-logo">
                    <i class="fas fa-music"></i>
                    <span>NEXUS</span>
                </div>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p class="loading-text">Initializing audio engine...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <div class="logo">
                        <i class="fas fa-music"></i>
                        <span>NEXUS</span>
                    </div>
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <h3>Library</h3>
                        <ul>
                            <li><a href="#" data-view="home" class="nav-link active"><i class="fas fa-home"></i> Home</a></li>
                            <li><a href="#" data-view="library" class="nav-link"><i class="fas fa-music"></i> Your Music</a></li>
                            <li><a href="#" data-view="playlists" class="nav-link"><i class="fas fa-list"></i> Playlists</a></li>
                            <li><a href="#" data-view="favorites" class="nav-link"><i class="fas fa-heart"></i> Favorites</a></li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>Discover</h3>
                        <ul>
                            <li><a href="#" data-view="genres" class="nav-link"><i class="fas fa-tags"></i> Genres</a></li>
                            <li><a href="#" data-view="artists" class="nav-link"><i class="fas fa-user-music"></i> Artists</a></li>
                            <li><a href="#" data-view="albums" class="nav-link"><i class="fas fa-compact-disc"></i> Albums</a></li>
                        </ul>
                    </div>
                </nav>

                <div class="sidebar-footer">
                    <button class="import-btn" id="import-music">
                        <i class="fas fa-plus"></i>
                        <span>Import Music</span>
                    </button>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Top Bar -->
                <header class="top-bar">
                    <div class="top-bar-left">
                        <div class="navigation-controls">
                            <button class="nav-btn" id="back-btn" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="nav-btn" id="forward-btn" disabled>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div class="search-container">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="Search for songs, artists, albums..." id="search-input">
                            <button class="search-clear" id="search-clear">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="top-bar-right">
                        <div class="user-controls">
                            <button class="control-btn" id="settings-btn" title="Settings">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button class="control-btn" id="fullscreen-btn" title="Fullscreen">
                                <i class="fas fa-expand"></i>
                            </button>
                            <div class="user-profile">
                                <img src="https://via.placeholder.com/32" alt="User" class="user-avatar">
                                <span class="user-name">Music Lover</span>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Content Views -->
                <div class="content-container">
                    <div id="home-view" class="content-view active">
                        <div class="welcome-section">
                            <h1>Welcome to Nexus!</h1>
                            <p>Your music library is automatically loaded from the MUSIC folder</p>
                            <div class="library-stats" id="library-stats">
                                <span class="stat-item">📁 Scanning music folder...</span>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <button class="quick-action-btn" id="quick-shuffle">
                                <i class="fas fa-random"></i>
                                <span>Shuffle All</span>
                                <p>Play all your music randomly</p>
                            </button>
                            <button class="quick-action-btn" id="quick-library">
                                <i class="fas fa-music"></i>
                                <span>Browse Library</span>
                                <p>Explore your music collection</p>
                            </button>
                            <button class="quick-action-btn" id="quick-favorites">
                                <i class="fas fa-heart"></i>
                                <span>Favorites</span>
                                <p>Your most loved tracks</p>
                            </button>
                        </div>

                        <div class="recent-section">
                            <h2>Recently Played</h2>
                            <div class="recent-tracks" id="recent-tracks">
                                <!-- Recent tracks will be populated here -->
                            </div>
                        </div>

                        <div class="artists-section">
                            <h2>Your Artists</h2>
                            <div class="artists-grid" id="artists-grid">
                                <!-- Artists will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div id="library-view" class="content-view">
                        <div class="view-header">
                            <h1>Your Music Library</h1>
                            <div class="view-controls">
                                <div class="sort-controls">
                                    <select id="sort-select" class="sort-select">
                                        <option value="title">Sort by Title</option>
                                        <option value="artist">Sort by Artist</option>
                                        <option value="album">Sort by Album</option>
                                        <option value="date">Sort by Date Added</option>
                                    </select>
                                </div>
                                <div class="view-toggle">
                                    <button class="view-toggle-btn active" data-view="grid">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button class="view-toggle-btn" data-view="list">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="music-grid" id="music-grid">
                            <!-- Music tracks will be populated here -->
                        </div>
                    </div>

                    <!-- Other views will be added dynamically -->
                </div>
            </main>

            <!-- Right Panel (Visualizer/Queue) -->
            <aside class="right-panel" id="right-panel">
                <div class="panel-tabs">
                    <button class="panel-tab active" data-tab="visualizer">
                        <i class="fas fa-wave-square"></i>
                        <span>Visualizer</span>
                    </button>
                    <button class="panel-tab" data-tab="queue">
                        <i class="fas fa-list-ol"></i>
                        <span>Queue</span>
                    </button>
                    <button class="panel-tab" data-tab="lyrics">
                        <i class="fas fa-align-left"></i>
                        <span>Lyrics</span>
                    </button>
                </div>

                <div class="panel-content">
                    <div id="visualizer-tab" class="tab-content active">
                        <div class="visualizer-container">
                            <canvas id="visualizer-canvas" class="visualizer-canvas"></canvas>
                            <div class="visualizer-controls">
                                <button class="viz-mode-btn active" data-mode="bars">Bars</button>
                                <button class="viz-mode-btn" data-mode="wave">Wave</button>
                                <button class="viz-mode-btn" data-mode="circle">Circle</button>
                                <button class="viz-mode-btn" data-mode="particles">Particles</button>
                            </div>
                        </div>
                    </div>

                    <div id="queue-tab" class="tab-content">
                        <div class="queue-header">
                            <h3>Up Next</h3>
                            <button class="clear-queue-btn" id="clear-queue">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="queue-list" id="queue-list">
                            <!-- Queue items will be populated here -->
                        </div>
                    </div>

                    <div id="lyrics-tab" class="tab-content">
                        <div class="lyrics-container" id="lyrics-container">
                            <p class="no-lyrics">No lyrics available</p>
                        </div>
                    </div>
                </div>
            </aside>
        </div>

        <!-- Player Bar -->
        <div class="player-bar" id="player-bar">
            <div class="player-info">
                <div class="track-artwork">
                    <img id="current-artwork" src="https://via.placeholder.com/60" alt="Track artwork">
                    <div class="artwork-overlay">
                        <button class="artwork-btn" id="expand-player">
                            <i class="fas fa-expand-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="track-details">
                    <div class="track-title" id="current-title">Select a track</div>
                    <div class="track-artist" id="current-artist">No artist</div>
                </div>
                <button class="favorite-btn" id="favorite-btn">
                    <i class="far fa-heart"></i>
                </button>
            </div>

            <div class="player-controls">
                <div class="control-buttons">
                    <button class="control-btn" id="shuffle-btn" title="Shuffle">
                        <i class="fas fa-random"></i>
                    </button>
                    <button class="control-btn" id="prev-btn" title="Previous">
                        <i class="fas fa-step-backward"></i>
                    </button>
                    <button class="control-btn play-pause-btn" id="play-pause-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="control-btn" id="next-btn" title="Next">
                        <i class="fas fa-step-forward"></i>
                    </button>
                    <button class="control-btn" id="repeat-btn" title="Repeat">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                <div class="progress-container">
                    <span class="time-display" id="current-time">0:00</span>
                    <div class="progress-bar" id="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                        <div class="progress-handle" id="progress-handle"></div>
                    </div>
                    <span class="time-display" id="total-time">0:00</span>
                </div>
            </div>

            <div class="player-extras">
                <button class="control-btn" id="queue-btn" title="Queue">
                    <i class="fas fa-list"></i>
                </button>
                <div class="volume-container">
                    <button class="control-btn" id="volume-btn" title="Volume">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <div class="volume-slider" id="volume-slider">
                        <div class="volume-fill" id="volume-fill"></div>
                        <div class="volume-handle" id="volume-handle"></div>
                    </div>
                </div>
                <button class="control-btn" id="visualizer-toggle" title="Visualizer (V)">
                    <i class="fas fa-wave-square"></i>
                </button>
                <button class="control-btn" id="equalizer-btn" title="Equalizer">
                    <i class="fas fa-sliders-h"></i>
                </button>
                <button class="control-btn" id="panel-toggle" title="Toggle Panel">
                    <i class="fas fa-sidebar"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Audio Visualizer -->
    <div class="visualizer-container" id="visualizer-container">
        <canvas id="visualizer-canvas"></canvas>
        <div class="visualizer-overlay">
            <div class="visualizer-info">
                <div class="frequency-display">
                    <div class="freq-bar" style="--height: 20%"></div>
                    <div class="freq-bar" style="--height: 40%"></div>
                    <div class="freq-bar" style="--height: 60%"></div>
                    <div class="freq-bar" style="--height: 80%"></div>
                    <div class="freq-bar" style="--height: 100%"></div>
                    <div class="freq-bar" style="--height: 75%"></div>
                    <div class="freq-bar" style="--height: 50%"></div>
                    <div class="freq-bar" style="--height: 30%"></div>
                    <div class="freq-bar" style="--height: 45%"></div>
                    <div class="freq-bar" style="--height: 65%"></div>
                    <div class="freq-bar" style="--height: 85%"></div>
                    <div class="freq-bar" style="--height: 95%"></div>
                    <div class="freq-bar" style="--height: 70%"></div>
                    <div class="freq-bar" style="--height: 55%"></div>
                    <div class="freq-bar" style="--height: 35%"></div>
                    <div class="freq-bar" style="--height: 25%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file input for music import -->
    <input type="file" id="music-file-input" multiple accept="audio/*" style="display: none;">

    <!-- Audio element -->
    <audio id="audio-player" crossorigin="anonymous"></audio>

    <!-- Scripts -->
    <script>
        // Enhanced music player with visualizer
        class SimpleMusicPlayer {
            constructor() {
                this.tracks = [];
                this.currentTrack = null;
                this.currentIndex = -1;
                this.audio = document.getElementById('audio-player');
                this.isPlaying = false;
                this.shuffle = false;
                this.repeat = 'none';
                this.volume = 1.0;

                // Visualizer properties
                this.audioContext = null;
                this.analyser = null;
                this.dataArray = null;
                this.canvas = null;
                this.canvasCtx = null;
                this.visualizerActive = false;

                this.initialize();
            }

            async initialize() {
                console.log('🎵 Initializing Simple Music Player...');

                // Hide loading screen
                setTimeout(() => {
                    const loadingScreen = document.getElementById('loading-screen');
                    const mainApp = document.getElementById('main-app');

                    if (loadingScreen) loadingScreen.style.display = 'none';
                    if (mainApp) mainApp.classList.remove('hidden');
                }, 1000);

                // Load music from server
                await this.loadMusicLibrary();

                // Setup event listeners
                this.setupEventListeners();

                // Initialize visualizer
                this.initializeVisualizer();

                console.log('✅ Enhanced Music Player initialized');
            }

            async loadMusicLibrary() {
                try {
                    console.log('🔍 Loading music library...');
                    const response = await fetch('/api/music-library');
                    const data = await response.json();

                    this.tracks = data.tracks || [];

                    // Update UI
                    this.updateLibraryStats(data.stats);
                    this.updateLibraryView();

                    console.log(`✅ Loaded ${this.tracks.length} tracks`);
                } catch (error) {
                    console.error('Failed to load music library:', error);
                    this.updateLibraryStats({ totalTracks: 0, totalArtists: 0, totalAlbums: 0 });
                }
            }

            updateLibraryStats(stats) {
                const statsEl = document.getElementById('library-stats');
                if (statsEl) {
                    statsEl.innerHTML = `
                        <span class="stat-item">🎵 ${stats.totalTracks} tracks</span>
                        <span class="stat-item">👨‍🎤 ${stats.totalArtists} artists</span>
                        <span class="stat-item">💿 ${stats.totalAlbums} albums</span>
                    `;
                }
            }

            updateLibraryView() {
                const musicGrid = document.getElementById('music-grid');
                if (!musicGrid) return;

                musicGrid.innerHTML = '';

                if (this.tracks.length === 0) {
                    musicGrid.innerHTML = '<div class="empty-state"><h3>No music found</h3><p>Make sure your MUSIC folder contains audio files</p></div>';
                    return;
                }

                this.tracks.forEach((track, index) => {
                    const card = document.createElement('div');
                    card.className = 'music-card';
                    card.innerHTML = `
                        <div class="card-artwork">
                            <img src="https://via.placeholder.com/200" alt="${track.title}">
                            <div class="play-overlay">
                                <button class="play-overlay-btn" onclick="player.playTrack(${index})">
                                    <i class="fas fa-play"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-info">
                            <div class="card-title">${track.title}</div>
                            <div class="card-artist">${track.artist}</div>
                        </div>
                    `;
                    musicGrid.appendChild(card);
                });
            }

            setupEventListeners() {
                // Play/pause button
                const playPauseBtn = document.getElementById('play-pause-btn');
                if (playPauseBtn) {
                    playPauseBtn.addEventListener('click', () => this.togglePlayPause());
                }

                // Next/previous buttons
                const nextBtn = document.getElementById('next-btn');
                const prevBtn = document.getElementById('prev-btn');
                if (nextBtn) nextBtn.addEventListener('click', () => this.playNext());
                if (prevBtn) prevBtn.addEventListener('click', () => this.playPrevious());

                // Shuffle button
                const shuffleBtn = document.getElementById('shuffle-btn');
                if (shuffleBtn) {
                    shuffleBtn.addEventListener('click', () => {
                        this.shuffle = !this.shuffle;
                        shuffleBtn.classList.toggle('active', this.shuffle);
                    });
                }

                // Quick action buttons
                const quickShuffle = document.getElementById('quick-shuffle');
                const quickLibrary = document.getElementById('quick-library');

                if (quickShuffle) {
                    quickShuffle.addEventListener('click', () => this.shuffleAll());
                }

                if (quickLibrary) {
                    quickLibrary.addEventListener('click', () => this.showLibrary());
                }

                // Navigation
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const view = link.dataset.view;
                        this.showView(view);
                    });
                });

                // Visualizer toggle
                const visualizerToggle = document.getElementById('visualizer-toggle');
                if (visualizerToggle) {
                    visualizerToggle.addEventListener('click', () => this.toggleVisualizer());
                }

                // Progress bar click
                const progressBar = document.getElementById('progress-bar');
                if (progressBar) {
                    progressBar.addEventListener('click', (e) => this.seekTo(e));
                }

                // Volume control
                const volumeRange = document.getElementById('volume-range');
                if (volumeRange) {
                    volumeRange.addEventListener('input', (e) => this.setVolume(e.target.value / 100));
                }

                // Audio events
                if (this.audio) {
                    this.audio.addEventListener('ended', () => this.playNext());
                    this.audio.addEventListener('timeupdate', () => this.updateProgress());
                    this.audio.addEventListener('loadedmetadata', () => this.updateProgress());
                }

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => this.handleKeyboard(e));

                // Close visualizer on escape
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.visualizerActive) {
                        this.toggleVisualizer();
                    }
                });
            }

            async playTrack(index) {
                if (index < 0 || index >= this.tracks.length) return;

                this.currentIndex = index;
                this.currentTrack = this.tracks[index];

                if (this.audio) {
                    this.audio.src = this.currentTrack.url;
                    try {
                        await this.audio.play();
                        this.isPlaying = true;
                        this.updatePlayButton();
                        this.updateTrackInfo();
                    } catch (error) {
                        console.error('Failed to play track:', error);
                    }
                }
            }

            togglePlayPause() {
                if (!this.audio) return;

                if (this.isPlaying) {
                    this.audio.pause();
                    this.isPlaying = false;
                } else {
                    if (this.currentTrack) {
                        this.audio.play();
                        this.isPlaying = true;
                    } else if (this.tracks.length > 0) {
                        this.playTrack(0);
                        return;
                    }
                }
                this.updatePlayButton();
            }

            playNext() {
                if (this.tracks.length === 0) return;

                let nextIndex = this.currentIndex + 1;
                if (nextIndex >= this.tracks.length) {
                    nextIndex = this.repeat === 'all' ? 0 : this.currentIndex;
                }

                if (nextIndex !== this.currentIndex || this.repeat === 'one') {
                    this.playTrack(nextIndex);
                }
            }

            playPrevious() {
                if (this.tracks.length === 0) return;

                let prevIndex = this.currentIndex - 1;
                if (prevIndex < 0) {
                    prevIndex = this.repeat === 'all' ? this.tracks.length - 1 : 0;
                }

                this.playTrack(prevIndex);
            }

            shuffleAll() {
                if (this.tracks.length === 0) return;

                const randomIndex = Math.floor(Math.random() * this.tracks.length);
                this.shuffle = true;
                const shuffleBtn = document.getElementById('shuffle-btn');
                if (shuffleBtn) shuffleBtn.classList.add('active');

                this.playTrack(randomIndex);
            }

            showLibrary() {
                this.showView('library');
            }

            showView(viewName) {
                // Hide all views
                const views = document.querySelectorAll('.content-view');
                views.forEach(view => view.classList.remove('active'));

                // Show selected view
                const targetView = document.getElementById(`${viewName}-view`);
                if (targetView) {
                    targetView.classList.add('active');
                }

                // Update navigation
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.classList.toggle('active', link.dataset.view === viewName);
                });
            }

            updatePlayButton() {
                const playPauseBtn = document.getElementById('play-pause-btn');
                if (playPauseBtn) {
                    const icon = playPauseBtn.querySelector('i');
                    if (icon) {
                        icon.className = this.isPlaying ? 'fas fa-pause' : 'fas fa-play';
                    }
                }
            }

            updateTrackInfo() {
                if (!this.currentTrack) return;

                const titleEl = document.getElementById('current-title');
                const artistEl = document.getElementById('current-artist');

                if (titleEl) titleEl.textContent = this.currentTrack.title;
                if (artistEl) artistEl.textContent = this.currentTrack.artist;
            }

            updateProgress() {
                if (!this.audio) return;

                const currentTime = this.audio.currentTime;
                const duration = this.audio.duration;

                if (duration) {
                    const progress = currentTime / duration;
                    const progressFill = document.getElementById('progress-fill');
                    if (progressFill) {
                        progressFill.style.width = `${progress * 100}%`;
                    }
                }

                // Update time displays
                const currentTimeEl = document.getElementById('current-time');
                const totalTimeEl = document.getElementById('total-time');

                if (currentTimeEl) currentTimeEl.textContent = this.formatTime(currentTime);
                if (totalTimeEl) totalTimeEl.textContent = this.formatTime(duration);
            }

            formatTime(seconds) {
                if (isNaN(seconds)) return '0:00';
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins}:${secs.toString().padStart(2, '0')}`;
            }

            // Initialize visualizer
            initializeVisualizer() {
                this.canvas = document.getElementById('visualizer-canvas');
                if (this.canvas) {
                    this.canvasCtx = this.canvas.getContext('2d');
                    this.resizeCanvas();
                    window.addEventListener('resize', () => this.resizeCanvas());
                }
            }

            resizeCanvas() {
                if (this.canvas) {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                }
            }

            async toggleVisualizer() {
                const container = document.getElementById('visualizer-container');
                if (!container) return;

                this.visualizerActive = !this.visualizerActive;

                if (this.visualizerActive) {
                    container.classList.add('active');
                    await this.startVisualizer();
                    this.animateVisualizer();
                } else {
                    container.classList.remove('active');
                    this.stopVisualizer();
                }
            }

            async startVisualizer() {
                if (!this.audioContext) {
                    try {
                        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const source = this.audioContext.createMediaElementSource(this.audio);
                        this.analyser = this.audioContext.createAnalyser();

                        source.connect(this.analyser);
                        this.analyser.connect(this.audioContext.destination);

                        this.analyser.fftSize = 256;
                        this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
                    } catch (error) {
                        console.error('Failed to initialize audio context:', error);
                    }
                }

                if (this.audioContext.state === 'suspended') {
                    await this.audioContext.resume();
                }
            }

            stopVisualizer() {
                // Visualizer stops automatically when container is hidden
            }

            animateVisualizer() {
                if (!this.visualizerActive || !this.analyser || !this.canvasCtx) return;

                this.analyser.getByteFrequencyData(this.dataArray);

                this.canvasCtx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                const barWidth = this.canvas.width / this.dataArray.length * 2;
                let x = 0;

                // Create gradient
                const gradient = this.canvasCtx.createLinearGradient(0, this.canvas.height, 0, 0);
                gradient.addColorStop(0, '#8a2be2');
                gradient.addColorStop(0.5, '#ff6b6b');
                gradient.addColorStop(1, '#4ecdc4');

                for (let i = 0; i < this.dataArray.length; i++) {
                    const barHeight = (this.dataArray[i] / 255) * this.canvas.height * 0.8;

                    this.canvasCtx.fillStyle = gradient;
                    this.canvasCtx.fillRect(x, this.canvas.height - barHeight, barWidth, barHeight);

                    // Add glow effect
                    this.canvasCtx.shadowColor = '#8a2be2';
                    this.canvasCtx.shadowBlur = 20;
                    this.canvasCtx.fillRect(x, this.canvas.height - barHeight, barWidth, barHeight);
                    this.canvasCtx.shadowBlur = 0;

                    x += barWidth + 2;
                }

                // Update frequency bars in overlay
                this.updateFrequencyBars();

                requestAnimationFrame(() => this.animateVisualizer());
            }

            updateFrequencyBars() {
                const freqBars = document.querySelectorAll('.freq-bar');
                if (!this.dataArray || freqBars.length === 0) return;

                const step = Math.floor(this.dataArray.length / freqBars.length);

                freqBars.forEach((bar, index) => {
                    const dataIndex = index * step;
                    const height = (this.dataArray[dataIndex] / 255) * 100;
                    bar.style.setProperty('--height', `${Math.max(height, 10)}%`);
                });
            }

            seekTo(event) {
                if (!this.audio || !this.audio.duration) return;

                const progressBar = event.currentTarget;
                const rect = progressBar.getBoundingClientRect();
                const clickX = event.clientX - rect.left;
                const percentage = clickX / rect.width;
                const newTime = percentage * this.audio.duration;

                this.audio.currentTime = newTime;
            }

            setVolume(volume) {
                this.volume = Math.max(0, Math.min(1, volume));
                if (this.audio) {
                    this.audio.volume = this.volume;
                }

                // Update volume icon
                const volumeBtn = document.getElementById('volume-btn');
                if (volumeBtn) {
                    const icon = volumeBtn.querySelector('i');
                    if (icon) {
                        if (this.volume === 0) {
                            icon.className = 'fas fa-volume-mute';
                        } else if (this.volume < 0.5) {
                            icon.className = 'fas fa-volume-down';
                        } else {
                            icon.className = 'fas fa-volume-up';
                        }
                    }
                }
            }

            handleKeyboard(event) {
                // Prevent default if we handle the key
                switch (event.code) {
                    case 'Space':
                        if (event.target.tagName !== 'INPUT') {
                            event.preventDefault();
                            this.togglePlayPause();
                        }
                        break;
                    case 'ArrowRight':
                        event.preventDefault();
                        this.playNext();
                        break;
                    case 'ArrowLeft':
                        event.preventDefault();
                        this.playPrevious();
                        break;
                    case 'KeyV':
                        if (event.ctrlKey || event.metaKey) return; // Don't interfere with paste
                        event.preventDefault();
                        this.toggleVisualizer();
                        break;
                }
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.player = new SimpleMusicPlayer();
        });
    </script>
</body>
</html>
