<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Local Music Player</title>
  <!-- === Styles ===================================================== -->
  <style>
    /* --- CSS RESET --- */
    *, *::before, *::after { box-sizing: border-box; margin: 0; padding: 0; }
    html, body { height: 100%; font-family: "Inter", system-ui, sans-serif; background: #111; color: #f5f5f5; }

    :root {
      --accent: #ff004e;
      --accent-light: #ff5685;
      --bg-dark: #0d0d0d;
      --bg-light: #1b1b1b;
      --radius: 1rem;
      --shadow: 0 4px 24px rgba(0,0,0,.6);
    }

    /* --- Layout --- */
    #app { display: flex; flex-direction: column; height: 100%; }

    header {
      display: flex; align-items: center; justify-content: space-between;
      padding: .75rem 1.5rem; background: var(--bg-light); box-shadow: var(--shadow);
    }
    header h1 { font-size: 1.25rem; font-weight: 600; letter-spacing: .5px; }
    header input[type="file"] { color: #fafafa; cursor: pointer; }

    .container { flex: 1; display: grid; grid-template-columns: 260px 1fr; overflow: hidden; }

    /* --- Library Sidebar --- */
    #library { background: var(--bg-dark); overflow-y: auto; padding: 1rem; }
    #artistList { list-style: none; display: flex; flex-direction: column; gap: .25rem; }
    #artistList li { cursor: pointer; padding: .35rem .5rem; border-radius: .5rem; transition: background .25s; user-select: none; }
    #artistList li:hover { background: var(--accent-light); }
    #artistList .active { background: var(--accent); }

    /* --- Player --- */
    #player { display: flex; flex-direction: column; height: 100%; padding: 1.25rem 1.5rem; gap: 1.25rem; overflow: hidden; }

    .now-playing { display: flex; align-items: center; gap: 1.25rem; }
    .now-playing img {
      width: 120px; height: 120px; object-fit: cover; border-radius: var(--radius);
      box-shadow: 0 0 12px rgba(255,255,255,.15);
    }
    .track-info { flex: 1; }
    .track-info h2 { font-size: 1.1rem; margin-bottom: .25rem; }
    .track-info p { font-size: .9rem; color: #ccc; }

    /* --- Visualizer Canvas --- */
    #visualizer {
      flex: 1; width: 100%; background: linear-gradient(180deg,#151515 0%,#0e0e0e 100%);
      border-radius: var(--radius); box-shadow: var(--shadow);
    }

    /* --- Controls --- */
    .controls { display: flex; align-items: center; gap: .75rem; }
    .controls button {
      border: none; background: var(--accent); color: #fff; width: 44px; height: 44px; border-radius: 50%;
      display: grid; place-content: center; font-size: 1.1rem; cursor: pointer; transition: transform .15s;
    }
    .controls button:hover { transform: scale(1.05); }
    .controls input[type="range"] {
      flex: 1; -webkit-appearance: none; height: 6px; border-radius: 3px; background: #666; outline: none; cursor: pointer;
    }
    .controls input[type="range"]::-webkit-slider-thumb { -webkit-appearance: none; width: 12px; height: 12px; border-radius: 50%; background: var(--accent); cursor: pointer; }
    .controls span { font-size: .8rem; width: 50px; text-align: center; }
  </style>
</head>
<body>
  <div id="app">
    <!-- ===================== HEADER ===================== -->
    <header>
      <h1>🎧 Local Music Player</h1>
      <input id="directoryPicker" type="file" webkitdirectory multiple />
    </header>

    <div class="container">
      <!-- ===================== LIBRARY ===================== -->
      <aside id="library">
        <ul id="artistList"><!-- Populated dynamically --></ul>
      </aside>

      <!-- ===================== PLAYER ===================== -->
      <main id="player">
        <div class="now-playing">
          <img id="albumArt" src="" alt="Album Art Placeholder" />
          <div class="track-info">
            <h2 id="trackTitle">Select a song</h2>
            <p id="trackArtist">–</p>
          </div>
        </div>

        <canvas id="visualizer"></canvas>

        <div class="controls">
          <button id="prevBtn" title="Previous">⏮</button>
          <button id="playPauseBtn" title="Play/Pause">▶️</button>
          <button id="nextBtn" title="Next">⏭</button>
          <input id="progress" type="range" value="0" min="0" max="100" />
          <span id="currentTime">0:00</span>
          <span id="duration">0:00</span>
        </div>
      </main>
    </div>
  </div>

  <audio id="audio" crossorigin="anonymous"></audio>

  <!-- === Scripts ==================================================== -->
  <script>
    /* --------------------------- Globals --------------------------- */
    const directoryPicker = document.getElementById('directoryPicker');
    const artistListEl     = document.getElementById('artistList');
    const audio            = document.getElementById('audio');
    const playPauseBtn     = document.getElementById('playPauseBtn');
    const prevBtn          = document.getElementById('prevBtn');
    const nextBtn          = document.getElementById('nextBtn');
    const progressEl       = document.getElementById('progress');
    const albumArtEl       = document.getElementById('albumArt');
    const trackTitleEl     = document.getElementById('trackTitle');
    const trackArtistEl    = document.getElementById('trackArtist');
    const currentTimeEl    = document.getElementById('currentTime');
    const durationEl       = document.getElementById('duration');
    const canvas           = document.getElementById('visualizer');
    const ctx              = canvas.getContext('2d');

    let library = {};         // { artist: { album: [ File, ... ] } }
    let trackQueue = [];      // Flattened list of File objects
    let currentIndex = -1;

    /* ----------------------- File Loading -------------------------- */
    directoryPicker.addEventListener('change', (e) => {
      library = {};
      trackQueue = [];
      currentIndex = -1;
      const files = Array.from(e.target.files).filter(f => f.type.startsWith('audio'));
      // Build nested structure
      files.forEach(file => {
        const parts = file.webkitRelativePath.split('/'); // MUSIC/ARTISTS/Artist/Albums/Album/track.mp3
        const artist = parts[2] || 'Unknown Artist';
        const album  = parts[4] || 'Unknown Album';
        if (!library[artist]) library[artist] = {};
        if (!library[artist][album]) library[artist][album] = [];
        library[artist][album].push(file);
        trackQueue.push(file);
      });
      renderLibrary();
    });

    function renderLibrary() {
      artistListEl.innerHTML = '';
      Object.keys(library).sort().forEach(artist => {
        const li = document.createElement('li');
        li.textContent = artist;
        li.addEventListener('click', () => {
          playArtist(artist);
          [...artistListEl.children].forEach(c => c.classList.remove('active'));
          li.classList.add('active');
        });
        artistListEl.appendChild(li);
      });
    }

    /* ----------------------- Playback ------------------------------ */
    function playArtist(artist) {
      trackQueue = [];
      Object.values(library[artist]).forEach(albumArr => { trackQueue.push(...albumArr); });
      currentIndex = 0;
      loadAndPlay(trackQueue[currentIndex]);
    }

    function loadAndPlay(file) {
      const objectURL = URL.createObjectURL(file);
      audio.src = objectURL;
      audio.play();

      // UI Updates
      trackTitleEl.textContent = file.name.replace(/\.mp3$/i, '');
      const artist = file.webkitRelativePath.split('/')[2] || 'Unknown Artist';
      trackArtistEl.textContent = artist;

      // Try to set album art if GIF/JPG/PNG exists in Album Art folder
      const basePathParts = file.webkitRelativePath.split('/');
      const artistDir = basePathParts.slice(0, 3).join('/');
      const albumArtDir = `${artistDir}/Album Art`;
      const albumArtFile = Array.from(directoryPicker.files).find(f => f.webkitRelativePath.startsWith(albumArtDir) && f.type.startsWith('image'));
      if (albumArtFile) {
        albumArtEl.src = URL.createObjectURL(albumArtFile);
      } else {
        albumArtEl.src = '';
      }

      playPauseBtn.textContent = '⏸';
    }

    playPauseBtn.addEventListener('click', () => {
      if (!audio.src) return;
      if (audio.paused) {
        audio.play();
        playPauseBtn.textContent = '⏸';
      } else {
        audio.pause();
        playPauseBtn.textContent = '▶️';
      }
    });

    prevBtn.addEventListener('click', () => {
      if (currentIndex > 0) {
        currentIndex--; loadAndPlay(trackQueue[currentIndex]);
      }
    });
    nextBtn.addEventListener('click', () => {
      if (currentIndex < trackQueue.length - 1) {
        currentIndex++; loadAndPlay(trackQueue[currentIndex]);
      }
    });

    audio.addEventListener('loadedmetadata', () => {
      durationEl.textContent = formatTime(audio.duration);
    });

    audio.addEventListener('timeupdate', () => {
      currentTimeEl.textContent = formatTime(audio.currentTime);
      const percent = (audio.currentTime / audio.duration) * 100;
      progressEl.value = percent || 0;
    });

    progressEl.addEventListener('input', () => {
      if (audio.duration) {
        audio.currentTime = (progressEl.value / 100) * audio.duration;
      }
    });

    audio.addEventListener('ended', () => {
      nextBtn.click();
    });

    /* --------------------- Time Formatter ------------------------- */
    function formatTime(time) {
      if (isNaN(time)) return '0:00';
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60).toString().padStart(2, '0');
      return `${minutes}:${seconds}`;
    }

    /* ------------------- Audio Visualizer ------------------------- */
    let audioContext, analyser, sourceNode;

    function setupVisualizer() {
      canvas.width = canvas.clientWidth;
      canvas.height = canvas.clientHeight;
      audioContext = new AudioContext();
      analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      sourceNode = audioContext.createMediaElementSource(audio);
      sourceNode.connect(analyser);
      analyser.connect(audioContext.destination);

      const barWidth = (canvas.width / bufferLength) * 2.5;
      let barHeight, x;

      function renderFrame() {
        requestAnimationFrame(renderFrame);
        x = 0;
        analyser.getByteFrequencyData(dataArray);

        ctx.fillStyle = '#0e0e0e';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        for (let i = 0; i < bufferLength; i++) {
          barHeight = dataArray[i];
          const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
          gradient.addColorStop(0, 'var(--accent)');
          gradient.addColorStop(1, 'transparent');
          ctx.fillStyle = gradient;
          ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
          x += barWidth + 1;
        }
      }
      renderFrame();
    }

    // Lazy init on first play
    audio.addEventListener('play', () => {
      if (!audioContext) setupVisualizer();
      if (audioContext.state === 'suspended') audioContext.resume();
    });

    /* ------------------------- End ------------------------------- */
  </script>
</body>
</html>