<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RetroBeats MP3 Player - Early 2000s Edition</title>
  <style>
    /* === EARLY 2000s RETRO STYLING === */
    *, *::before, *::after { box-sizing: border-box; margin: 0; padding: 0; }

    @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

    :root {
      --primary: #00d4ff;
      --secondary: #ff6b35;
      --accent: #ffd700;
      --success: #00ff88;
      --danger: #ff3366;
      --bg-main: linear-gradient(145deg, #1a1a2e, #16213e);
      --bg-panel: linear-gradient(145deg, #2a2a3e, #1e1e32);
      --bg-button: linear-gradient(145deg, #3a3a4e, #2e2e42);
      --bg-active: linear-gradient(145deg, #4a4a5e, #3e3e52);
      --text-glow: 0 0 10px rgba(0, 212, 255, 0.8);
      --border-glow: 0 0 20px rgba(0, 212, 255, 0.4);
      --shadow-deep: 0 10px 40px rgba(0, 0, 0, 0.8);
      --shadow-inset: inset 0 2px 8px rgba(0, 0, 0, 0.4);
      --glass-effect: rgba(255, 255, 255, 0.1);
    }

    html, body {
      height: 100%;
      font-family: 'Orbitron', 'Tahoma', monospace;
      background: var(--bg-main);
      color: #e0e0e0;
      overflow: hidden;
      user-select: none;
    }

    /* === MAIN LAYOUT === */
    .player-container {
      display: grid;
      grid-template-areas:
        "header header header"
        "library visualizer controls"
        "footer footer footer";
      grid-template-rows: 60px 1fr 120px;
      grid-template-columns: 300px 1fr 350px;
      height: 100vh;
      gap: 2px;
      background: #0a0a0a;
      padding: 2px;
    }

    /* === HEADER === */
    .header {
      grid-area: header;
      background: var(--bg-panel);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      border: 2px solid rgba(0, 212, 255, 0.3);
      box-shadow: var(--shadow-deep), var(--shadow-inset);
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
      animation: scan 3s infinite;
    }

    @keyframes scan {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .logo {
      font-size: 24px;
      font-weight: 900;
      color: var(--primary);
      text-shadow: var(--text-glow);
      letter-spacing: 2px;
    }

    .header-controls {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .file-input-wrapper {
      position: relative;
      overflow: hidden;
      background: var(--bg-button);
      border: 1px solid var(--primary);
      border-radius: 8px;
      padding: 8px 16px;
      cursor: pointer;
      transition: all 0.3s;
      box-shadow: var(--shadow-inset);
    }

    .file-input-wrapper:hover {
      box-shadow: var(--border-glow), var(--shadow-inset);
      transform: translateY(-1px);
    }

    .file-input-wrapper input {
      position: absolute;
      left: -9999px;
    }

    /* === LIBRARY PANEL === */
    .library {
      grid-area: library;
      background: var(--bg-panel);
      border: 2px solid rgba(0, 212, 255, 0.2);
      box-shadow: var(--shadow-deep), var(--shadow-inset);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .library-header {
      padding: 15px;
      background: var(--bg-active);
      border-bottom: 1px solid rgba(0, 212, 255, 0.3);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .library-title {
      font-size: 16px;
      font-weight: 700;
      color: var(--primary);
      text-shadow: var(--text-glow);
    }

    .search-box {
      background: rgba(0, 0, 0, 0.5);
      border: 1px solid var(--primary);
      border-radius: 15px;
      padding: 5px 12px;
      color: #fff;
      font-size: 12px;
      width: 150px;
      outline: none;
      transition: all 0.3s;
    }

    .search-box:focus {
      box-shadow: var(--border-glow);
      background: rgba(0, 0, 0, 0.8);
    }

    .library-content {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
    }

    .library-tabs {
      display: flex;
      margin-bottom: 10px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 8px;
      padding: 2px;
    }

    .tab-button {
      flex: 1;
      padding: 8px;
      background: transparent;
      border: none;
      color: #ccc;
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.3s;
      font-size: 11px;
      font-weight: 600;
    }

    .tab-button.active {
      background: var(--bg-button);
      color: var(--primary);
      text-shadow: var(--text-glow);
      box-shadow: var(--shadow-inset);
    }

    .music-list {
      list-style: none;
    }

    .music-item {
      padding: 8px 12px;
      margin: 2px 0;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s;
      border-left: 3px solid transparent;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .music-item:hover {
      background: rgba(0, 212, 255, 0.1);
      border-left-color: var(--primary);
      transform: translateX(3px);
    }

    .music-item.playing {
      background: rgba(0, 212, 255, 0.2);
      border-left-color: var(--primary);
      color: var(--primary);
      text-shadow: var(--text-glow);
    }

    .music-item-info {
      flex: 1;
      min-width: 0;
    }

    .music-item-title {
      font-size: 13px;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .music-item-artist {
      font-size: 11px;
      color: #aaa;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .music-item-duration {
      font-size: 11px;
      color: var(--accent);
      font-weight: 600;
    }

    /* === VISUALIZER === */
    .visualizer {
      grid-area: visualizer;
      background: var(--bg-panel);
      border: 2px solid rgba(0, 212, 255, 0.2);
      box-shadow: var(--shadow-deep), var(--shadow-inset);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative;
    }

    .visualizer-header {
      padding: 15px;
      background: var(--bg-active);
      border-bottom: 1px solid rgba(0, 212, 255, 0.3);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .now-playing-info {
      flex: 1;
      min-width: 0;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .album-art {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      border: 2px solid var(--primary);
      box-shadow: var(--border-glow);
      object-fit: cover;
      background: var(--bg-button);
    }

    .track-info {
      flex: 1;
      min-width: 0;
    }

    .track-title {
      font-size: 18px;
      font-weight: 700;
      color: var(--primary);
      text-shadow: var(--text-glow);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 5px;
    }

    .track-artist {
      font-size: 14px;
      color: var(--secondary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .visualizer-modes {
      display: flex;
      gap: 5px;
    }

    .viz-mode-btn {
      padding: 5px 10px;
      background: var(--bg-button);
      border: 1px solid rgba(0, 212, 255, 0.3);
      border-radius: 4px;
      color: #ccc;
      cursor: pointer;
      font-size: 10px;
      transition: all 0.3s;
    }

    .viz-mode-btn.active {
      background: var(--primary);
      color: #000;
      box-shadow: var(--border-glow);
    }

    .visualizer-canvas {
      flex: 1;
      background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1), rgba(0, 0, 0, 0.8));
      position: relative;
      overflow: hidden;
    }

    #visualizer {
      width: 100%;
      height: 100%;
      display: block;
    }

    /* === CONTROLS PANEL === */
    .controls {
      grid-area: controls;
      background: var(--bg-panel);
      border: 2px solid rgba(0, 212, 255, 0.2);
      box-shadow: var(--shadow-deep), var(--shadow-inset);
      display: flex;
      flex-direction: column;
      padding: 20px;
      gap: 15px;
    }

    .volume-section {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .volume-label {
      font-size: 12px;
      color: var(--primary);
      font-weight: 600;
      min-width: 60px;
    }

    .slider {
      flex: 1;
      -webkit-appearance: none;
      height: 6px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 3px;
      outline: none;
      border: 1px solid rgba(0, 212, 255, 0.3);
    }

    .slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 16px;
      height: 16px;
      background: var(--primary);
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
    }

    .equalizer {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 8px;
      padding: 15px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 8px;
      border: 1px solid rgba(0, 212, 255, 0.2);
    }

    .eq-band {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
    }

    .eq-slider {
      writing-mode: bt-lr;
      -webkit-appearance: slider-vertical;
      width: 20px;
      height: 80px;
      background: rgba(0, 0, 0, 0.5);
      outline: none;
    }

    .eq-label {
      font-size: 10px;
      color: var(--accent);
      font-weight: 600;
    }

    .mode-toggles {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
    }

    .toggle-btn {
      padding: 8px;
      background: var(--bg-button);
      border: 1px solid rgba(0, 212, 255, 0.3);
      border-radius: 6px;
      color: #ccc;
      cursor: pointer;
      font-size: 11px;
      font-weight: 600;
      transition: all 0.3s;
      text-align: center;
    }

    .toggle-btn.active {
      background: var(--primary);
      color: #000;
      box-shadow: var(--border-glow);
    }

    /* === FOOTER CONTROLS === */
    .footer {
      grid-area: footer;
      background: var(--bg-panel);
      border: 2px solid rgba(0, 212, 255, 0.2);
      box-shadow: var(--shadow-deep), var(--shadow-inset);
      display: flex;
      flex-direction: column;
      padding: 15px 20px;
      gap: 10px;
    }

    .progress-section {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .time-display {
      font-size: 12px;
      color: var(--accent);
      font-weight: 600;
      min-width: 45px;
      text-align: center;
    }

    .progress-bar {
      flex: 1;
      height: 8px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      border: 1px solid rgba(0, 212, 255, 0.3);
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--primary), var(--secondary));
      border-radius: 3px;
      transition: width 0.1s;
      box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    }

    .main-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
    }

    .control-btn {
      width: 50px;
      height: 50px;
      background: var(--bg-button);
      border: 2px solid var(--primary);
      border-radius: 50%;
      color: var(--primary);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      transition: all 0.3s;
      box-shadow: var(--shadow-inset);
    }

    .control-btn:hover {
      transform: translateY(-2px);
      box-shadow: var(--border-glow), var(--shadow-inset);
    }

    .control-btn.play-pause {
      width: 60px;
      height: 60px;
      font-size: 24px;
      background: var(--primary);
      color: #000;
    }

    .control-btn.play-pause:hover {
      background: var(--secondary);
    }

    /* === SCROLLBAR STYLING === */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--primary);
      border-radius: 4px;
      box-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--secondary);
    }

    /* === RESPONSIVE === */
    @media (max-width: 1200px) {
      .player-container {
        grid-template-columns: 250px 1fr 300px;
      }
    }

    @media (max-width: 900px) {
      .player-container {
        grid-template-areas:
          "header header"
          "visualizer controls"
          "library library"
          "footer footer";
        grid-template-rows: 60px 1fr 200px 120px;
        grid-template-columns: 1fr 300px;
      }
    }
  </style>
</head>
<body>
  <div class="player-container">
    <!-- HEADER -->
    <div class="header">
      <div class="logo">🎵 RETROBEATS</div>
      <div class="header-controls">
        <div class="file-input-wrapper" onclick="document.getElementById('musicInput').click()">
          <input type="file" id="musicInput" webkitdirectory multiple accept="audio/*,image/*" style="display: none;">
          <span>📁 Load Your MUSIC Folder</span>
        </div>
        <div class="file-input-wrapper" id="helpBtn">
          <span>❓ How to Load</span>
        </div>
        <div class="file-input-wrapper" id="testBtn" style="display: none;">
          <span>🔧 Test Upload</span>
        </div>
        <div class="file-input-wrapper" id="testBtn">
          <span>🔧 Test Upload</span>
        </div>
      </div>
    </div>

    <!-- LIBRARY -->
    <div class="library">
      <div class="library-header">
        <div class="library-title">MUSIC LIBRARY</div>
        <input type="text" class="search-box" id="searchBox" placeholder="Search...">
      </div>
      <div class="library-content">
        <div class="library-tabs">
          <button class="tab-button active" data-tab="all">ALL</button>
          <button class="tab-button" data-tab="artists">ARTISTS</button>
          <button class="tab-button" data-tab="albums">ALBUMS</button>
        </div>
        <ul class="music-list" id="musicList">
          <!-- Music items will be populated here -->
        </ul>
      </div>
    </div>

    <!-- VISUALIZER -->
    <div class="visualizer">
      <div class="visualizer-header">
        <div class="now-playing-info">
          <img class="album-art" id="albumArt" src="" alt="Album Art">
          <div class="track-info">
            <div class="track-title" id="trackTitle">Select a track to play</div>
            <div class="track-artist" id="trackArtist">No artist</div>
          </div>
        </div>
        <div class="visualizer-modes">
          <button class="viz-mode-btn active" data-mode="spectrum">SPECTRUM</button>
          <button class="viz-mode-btn" data-mode="waveform">WAVE</button>
          <button class="viz-mode-btn" data-mode="circular">CIRCLE</button>
        </div>
      </div>
      <div class="visualizer-canvas">
        <canvas id="visualizer"></canvas>
      </div>
    </div>

    <!-- CONTROLS -->
    <div class="controls">
      <div class="volume-section">
        <div class="volume-label">VOLUME</div>
        <input type="range" class="slider" id="volumeSlider" min="0" max="100" value="50">
      </div>

      <div class="equalizer">
        <div class="eq-band">
          <input type="range" class="eq-slider" min="-12" max="12" value="0" data-freq="60">
          <div class="eq-label">60Hz</div>
        </div>
        <div class="eq-band">
          <input type="range" class="eq-slider" min="-12" max="12" value="0" data-freq="170">
          <div class="eq-label">170Hz</div>
        </div>
        <div class="eq-band">
          <input type="range" class="eq-slider" min="-12" max="12" value="0" data-freq="310">
          <div class="eq-label">310Hz</div>
        </div>
        <div class="eq-band">
          <input type="range" class="eq-slider" min="-12" max="12" value="0" data-freq="600">
          <div class="eq-label">600Hz</div>
        </div>
        <div class="eq-band">
          <input type="range" class="eq-slider" min="-12" max="12" value="0" data-freq="1000">
          <div class="eq-label">1kHz</div>
        </div>
      </div>

      <div class="mode-toggles">
        <button class="toggle-btn" id="shuffleBtn">🔀 SHUFFLE</button>
        <button class="toggle-btn" id="repeatBtn">🔁 REPEAT</button>
      </div>
    </div>

    <!-- FOOTER -->
    <div class="footer">
      <div class="progress-section">
        <div class="time-display" id="currentTime">0:00</div>
        <div class="progress-bar" id="progressBar">
          <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="time-display" id="totalTime">0:00</div>
      </div>

      <div class="main-controls">
        <button class="control-btn" id="prevBtn">⏮️</button>
        <button class="control-btn play-pause" id="playPauseBtn">▶️</button>
        <button class="control-btn" id="nextBtn">⏭️</button>
      </div>
    </div>
  </div>

  <audio id="audioPlayer" crossorigin="anonymous"></audio>

  <script>
    // === GLOBAL VARIABLES ===
    class MusicPlayer {
      constructor() {
        this.audioPlayer = document.getElementById('audioPlayer');
        this.musicLibrary = [];
        this.currentTrackIndex = -1;
        this.isPlaying = false;
        this.isShuffled = false;
        this.repeatMode = 'none'; // none, one, all
        this.currentTab = 'all';
        this.searchQuery = '';
        this.visualizerMode = 'spectrum';

        // Audio context for visualizer
        this.audioContext = null;
        this.analyser = null;
        this.sourceNode = null;
        this.dataArray = null;
        this.bufferLength = 0;

        this.initializeElements();
        this.setupEventListeners();
        this.setupVisualizer();
        this.loadExistingMusic();
      }

      initializeElements() {
        // UI Elements
        this.musicInput = document.getElementById('musicInput');
        this.musicList = document.getElementById('musicList');
        this.searchBox = document.getElementById('searchBox');
        this.playPauseBtn = document.getElementById('playPauseBtn');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.progressBar = document.getElementById('progressBar');
        this.progressFill = document.getElementById('progressFill');
        this.currentTime = document.getElementById('currentTime');
        this.totalTime = document.getElementById('totalTime');
        this.trackTitle = document.getElementById('trackTitle');
        this.trackArtist = document.getElementById('trackArtist');
        this.albumArt = document.getElementById('albumArt');
        this.volumeSlider = document.getElementById('volumeSlider');
        this.shuffleBtn = document.getElementById('shuffleBtn');
        this.repeatBtn = document.getElementById('repeatBtn');
        this.visualizerCanvas = document.getElementById('visualizer');
        this.tabButtons = document.querySelectorAll('.tab-button');
        this.vizModeButtons = document.querySelectorAll('.viz-mode-btn');
        this.eqSliders = document.querySelectorAll('.eq-slider');
        this.helpBtn = document.getElementById('helpBtn');
        this.testBtn = document.getElementById('testBtn');
        this.testBtn = document.getElementById('testBtn');
      }

      setupEventListeners() {
        // File input with better error handling
        this.musicInput.addEventListener('change', (e) => {
          try {
            this.handleFileInput(e);
          } catch (error) {
            console.error('Error loading files:', error);
            this.showMessage('Error loading files. Please try again.');
          }
        });

        // Playback controls
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        this.prevBtn.addEventListener('click', () => this.previousTrack());
        this.nextBtn.addEventListener('click', () => this.nextTrack());

        // Progress bar
        this.progressBar.addEventListener('click', (e) => this.seekTo(e));

        // Volume control
        this.volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value));

        // Mode toggles
        this.shuffleBtn.addEventListener('click', () => this.toggleShuffle());
        this.repeatBtn.addEventListener('click', () => this.toggleRepeat());

        // Search
        this.searchBox.addEventListener('input', (e) => this.handleSearch(e.target.value));

        // Tab switching
        this.tabButtons.forEach(btn => {
          btn.addEventListener('click', () => this.switchTab(btn.dataset.tab));
        });

        // Visualizer mode switching
        this.vizModeButtons.forEach(btn => {
          btn.addEventListener('click', () => this.switchVisualizerMode(btn.dataset.mode));
        });

        // Audio events
        this.audioPlayer.addEventListener('loadedmetadata', () => this.updateTrackInfo());
        this.audioPlayer.addEventListener('timeupdate', () => this.updateProgress());
        this.audioPlayer.addEventListener('ended', () => this.handleTrackEnd());
        this.audioPlayer.addEventListener('play', () => this.handlePlay());
        this.audioPlayer.addEventListener('pause', () => this.handlePause());

        // Equalizer
        this.eqSliders.forEach(slider => {
          slider.addEventListener('input', () => this.updateEqualizer());
        });

        // Help button
        this.helpBtn.addEventListener('click', () => this.showHelp());

        // Test button (hidden but keeps functionality working)
        this.testBtn.addEventListener('click', () => this.testUpload());

        // Test button
        this.testBtn.addEventListener('click', () => this.testUpload());
      }

      async loadExistingMusic() {
        // Show instructions for loading existing music
        this.showMessage('Click "📁 Load Music" and select your MUSIC folder to load your collection');

        // Create a helpful instruction in the library
        this.musicList.innerHTML = `
          <li class="music-item" style="background: rgba(0, 212, 255, 0.1); border-left: 3px solid var(--primary); cursor: default;">
            <div class="music-item-info">
              <div class="music-item-title">🎵 Welcome to RetroBeats!</div>
              <div class="music-item-artist">Click "📁 Load Music" above and select your MUSIC folder</div>
            </div>
          </li>
          <li class="music-item" style="background: rgba(255, 107, 53, 0.1); border-left: 3px solid var(--secondary); cursor: default;">
            <div class="music-item-info">
              <div class="music-item-title">📁 Your Music Structure</div>
              <div class="music-item-artist">MUSIC → ARTISTS → [Artist] → Albums → [Album] → songs.mp3</div>
            </div>
          </li>
          <li class="music-item" style="background: rgba(255, 215, 0, 0.1); border-left: 3px solid var(--accent); cursor: default;">
            <div class="music-item-info">
              <div class="music-item-title">🖼️ Album Art Support</div>
              <div class="music-item-artist">Place images in "Album Art" folder next to artist folder</div>
            </div>
          </li>
        `;
      }

      handleFileInput(event) {
        console.log('File input triggered');
        const files = Array.from(event.target.files);
        console.log(`Total files selected: ${files.length}`);

        if (files.length === 0) {
          this.showMessage('No files selected. Please try selecting your MUSIC folder again.');
          return;
        }

        // Show loading message
        this.showMessage('Loading your music collection...');

        const audioFiles = files.filter(file => file.type.startsWith('audio/'));
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        console.log(`Audio files found: ${audioFiles.length}`);
        console.log(`Image files found: ${imageFiles.length}`);

        if (audioFiles.length === 0) {
          this.showMessage('❌ No audio files found. Make sure you selected the MUSIC folder containing your songs.');
          return;
        }

        this.musicLibrary = [];
        this.albumArtCache = new Map(); // Cache for album art

        // Process image files first for album art
        imageFiles.forEach(file => {
          const pathParts = file.webkitRelativePath.split('/');
          if (pathParts.includes('Album Art')) {
            const artistIndex = pathParts.indexOf('ARTISTS');
            if (artistIndex >= 0 && artistIndex + 1 < pathParts.length) {
              const artist = pathParts[artistIndex + 1];
              this.albumArtCache.set(artist, file);
            }
          }
        });

        audioFiles.forEach(file => {
          const pathParts = file.webkitRelativePath.split('/');
          const fileName = file.name.replace(/\.[^/.]+$/, ''); // Remove extension

          // Enhanced path parsing for your structure: MUSIC/ARTISTS/ArtistName/Albums/AlbumName/song.mp3
          let artist = 'Unknown Artist';
          let album = 'Unknown Album';

          if (pathParts.length >= 3) {
            if (pathParts[0] === 'MUSIC' && pathParts[1] === 'ARTISTS') {
              // Your exact structure
              if (pathParts.length >= 6) {
                artist = pathParts[2]; // Artist name
                album = pathParts[4];  // Album name (Albums/AlbumName)
              } else if (pathParts.length >= 4) {
                artist = pathParts[2];
                album = pathParts[3] || 'Unknown Album';
              }
            } else if (pathParts[1] === 'ARTISTS') {
              // Alternative structure
              if (pathParts.length >= 5) {
                artist = pathParts[2];
                album = pathParts[4];
              }
            } else {
              // Fallback: use folder names
              artist = pathParts[pathParts.length - 2] || 'Unknown Artist';
              if (pathParts.length >= 2) {
                album = pathParts[pathParts.length - 3] || 'Unknown Album';
              }
            }
          }

          this.musicLibrary.push({
            file: file,
            title: fileName,
            artist: artist,
            album: album,
            duration: 0,
            path: file.webkitRelativePath,
            albumArt: this.albumArtCache.get(artist) || null
          });
        });

        // Sort library by artist, then album, then title
        this.musicLibrary.sort((a, b) => {
          if (a.artist !== b.artist) return a.artist.localeCompare(b.artist);
          if (a.album !== b.album) return a.album.localeCompare(b.album);
          return a.title.localeCompare(b.title);
        });

        this.renderMusicList();
        this.showMessage(`🎵 Loaded ${audioFiles.length} tracks from ${new Set(this.musicLibrary.map(t => t.artist)).size} artists`);

        // Auto-play first track if none is playing
        if (this.currentTrackIndex === -1 && this.musicLibrary.length > 0) {
          this.showMessage('Click any track to start playing, or use the play button!');
        }
      }

      renderMusicList() {
        let filteredLibrary = [...this.musicLibrary];

        // Apply search filter
        if (this.searchQuery) {
          filteredLibrary = filteredLibrary.filter(track =>
            track.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
            track.artist.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
            track.album.toLowerCase().includes(this.searchQuery.toLowerCase())
          );
        }

        // Apply tab filter
        if (this.currentTab === 'artists') {
          // Group by artists
          const artistGroups = {};
          filteredLibrary.forEach(track => {
            if (!artistGroups[track.artist]) {
              artistGroups[track.artist] = [];
            }
            artistGroups[track.artist].push(track);
          });

          this.musicList.innerHTML = '';
          Object.keys(artistGroups).sort().forEach(artist => {
            const artistHeader = document.createElement('li');
            artistHeader.className = 'music-item artist-header';
            artistHeader.innerHTML = `
              <div class="music-item-info">
                <div class="music-item-title">👤 ${artist}</div>
                <div class="music-item-artist">${artistGroups[artist].length} tracks</div>
              </div>
            `;
            this.musicList.appendChild(artistHeader);

            artistGroups[artist].forEach(track => {
              this.createMusicItem(track, true);
            });
          });
        } else if (this.currentTab === 'albums') {
          // Group by albums
          const albumGroups = {};
          filteredLibrary.forEach(track => {
            const key = `${track.artist} - ${track.album}`;
            if (!albumGroups[key]) {
              albumGroups[key] = [];
            }
            albumGroups[key].push(track);
          });

          this.musicList.innerHTML = '';
          Object.keys(albumGroups).sort().forEach(albumKey => {
            const albumHeader = document.createElement('li');
            albumHeader.className = 'music-item album-header';
            albumHeader.innerHTML = `
              <div class="music-item-info">
                <div class="music-item-title">💿 ${albumKey}</div>
                <div class="music-item-artist">${albumGroups[albumKey].length} tracks</div>
              </div>
            `;
            this.musicList.appendChild(albumHeader);

            albumGroups[albumKey].forEach(track => {
              this.createMusicItem(track, true);
            });
          });
        } else {
          // Show all tracks
          this.musicList.innerHTML = '';
          filteredLibrary.forEach(track => {
            this.createMusicItem(track);
          });
        }
      }

      createMusicItem(track, isSubItem = false) {
        const li = document.createElement('li');
        li.className = `music-item ${isSubItem ? 'sub-item' : ''}`;
        li.style.paddingLeft = isSubItem ? '30px' : '12px';

        // Add a small album art thumbnail if available
        const albumArtThumb = track.albumArt ?
          `<img src="${URL.createObjectURL(track.albumArt)}" style="width: 20px; height: 20px; border-radius: 3px; margin-right: 8px; object-fit: cover;">` :
          '🎵';

        li.innerHTML = `
          <div style="display: flex; align-items: center; gap: 8px; flex: 1; min-width: 0;">
            ${albumArtThumb}
            <div class="music-item-info">
              <div class="music-item-title">${track.title}</div>
              <div class="music-item-artist">${track.artist} • ${track.album}</div>
            </div>
          </div>
          <div class="music-item-duration">--:--</div>
        `;

        li.addEventListener('click', () => {
          const trackIndex = this.musicLibrary.indexOf(track);
          this.playTrack(trackIndex);
        });

        // Add double-click to play immediately
        li.addEventListener('dblclick', () => {
          const trackIndex = this.musicLibrary.indexOf(track);
          this.playTrack(trackIndex);
        });

        this.musicList.appendChild(li);
      }

      playTrack(index) {
        if (index < 0 || index >= this.musicLibrary.length) return;

        this.currentTrackIndex = index;
        const track = this.musicLibrary[index];

        // Create object URL for the file
        const objectURL = URL.createObjectURL(track.file);
        this.audioPlayer.src = objectURL;

        // Update UI
        this.updateNowPlaying(track);
        this.updatePlayingState();

        // Play the track
        this.audioPlayer.play().catch(e => {
          console.error('Error playing track:', e);
          this.showMessage('Error playing track');
        });
      }

      updateNowPlaying(track) {
        this.trackTitle.textContent = track.title;
        this.trackArtist.textContent = track.artist;

        // Try to find album art
        this.findAlbumArt(track);
      }

      async findAlbumArt(track) {
        // First check if we have cached album art for this artist
        if (track.albumArt) {
          this.albumArt.src = URL.createObjectURL(track.albumArt);
          return;
        }

        // Look for album art in the cached images
        const artistArt = this.albumArtCache?.get(track.artist);
        if (artistArt) {
          this.albumArt.src = URL.createObjectURL(artistArt);
          return;
        }

        // Fallback: look for any image files in the directory structure
        const pathParts = track.path.split('/');
        if (pathParts.length >= 3) {
          let albumArtPath = '';

          if (pathParts[0] === 'MUSIC' && pathParts[1] === 'ARTISTS') {
            // Your structure: MUSIC/ARTISTS/ArtistName/Album Art/
            albumArtPath = `MUSIC/ARTISTS/${track.artist}/Album Art`;
          } else if (pathParts[1] === 'ARTISTS') {
            // Alternative structure
            albumArtPath = `${pathParts[0]}/ARTISTS/${track.artist}/Album Art`;
          }

          // Try to find any image file in the album art folder
          if (this.albumArtCache) {
            for (let [artist, imageFile] of this.albumArtCache.entries()) {
              if (artist === track.artist) {
                this.albumArt.src = URL.createObjectURL(imageFile);
                return;
              }
            }
          }
        }

        // No album art found, use default
        this.albumArt.src = '';
        this.albumArt.style.background = 'var(--bg-button)';
      }

      updatePlayingState() {
        // Update music list to show currently playing track
        const musicItems = this.musicList.querySelectorAll('.music-item:not(.artist-header):not(.album-header)');
        musicItems.forEach((item, index) => {
          item.classList.remove('playing');
        });

        if (this.currentTrackIndex >= 0) {
          const currentItem = musicItems[this.currentTrackIndex];
          if (currentItem) {
            currentItem.classList.add('playing');
          }
        }
      }

      togglePlayPause() {
        if (!this.audioPlayer.src) {
          if (this.musicLibrary.length > 0) {
            this.playTrack(0);
          }
          return;
        }

        if (this.audioPlayer.paused) {
          this.audioPlayer.play();
        } else {
          this.audioPlayer.pause();
        }
      }

      previousTrack() {
        if (this.currentTrackIndex > 0) {
          this.playTrack(this.currentTrackIndex - 1);
        } else if (this.repeatMode === 'all') {
          this.playTrack(this.musicLibrary.length - 1);
        }
      }

      nextTrack() {
        if (this.currentTrackIndex < this.musicLibrary.length - 1) {
          this.playTrack(this.currentTrackIndex + 1);
        } else if (this.repeatMode === 'all') {
          this.playTrack(0);
        }
      }

      handleTrackEnd() {
        if (this.repeatMode === 'one') {
          this.audioPlayer.currentTime = 0;
          this.audioPlayer.play();
        } else {
          this.nextTrack();
        }
      }

      handlePlay() {
        this.isPlaying = true;
        this.playPauseBtn.textContent = '⏸️';
        this.initAudioContext();
      }

      handlePause() {
        this.isPlaying = false;
        this.playPauseBtn.textContent = '▶️';
      }

      seekTo(event) {
        if (!this.audioPlayer.duration) return;

        const rect = this.progressBar.getBoundingClientRect();
        const percent = (event.clientX - rect.left) / rect.width;
        this.audioPlayer.currentTime = percent * this.audioPlayer.duration;
      }

      updateProgress() {
        if (!this.audioPlayer.duration) return;

        const percent = (this.audioPlayer.currentTime / this.audioPlayer.duration) * 100;
        this.progressFill.style.width = `${percent}%`;

        this.currentTime.textContent = this.formatTime(this.audioPlayer.currentTime);
      }

      updateTrackInfo() {
        this.totalTime.textContent = this.formatTime(this.audioPlayer.duration);
      }

      formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
      }

      setVolume(value) {
        this.audioPlayer.volume = value / 100;
      }

      toggleShuffle() {
        this.isShuffled = !this.isShuffled;
        this.shuffleBtn.classList.toggle('active', this.isShuffled);
      }

      toggleRepeat() {
        const modes = ['none', 'one', 'all'];
        const currentIndex = modes.indexOf(this.repeatMode);
        this.repeatMode = modes[(currentIndex + 1) % modes.length];

        this.repeatBtn.classList.toggle('active', this.repeatMode !== 'none');

        const icons = { none: '🔁', one: '🔂', all: '🔁' };
        this.repeatBtn.innerHTML = `${icons[this.repeatMode]} REPEAT`;
      }

      handleSearch(query) {
        this.searchQuery = query;
        this.renderMusicList();
      }

      switchTab(tab) {
        this.currentTab = tab;
        this.tabButtons.forEach(btn => {
          btn.classList.toggle('active', btn.dataset.tab === tab);
        });
        this.renderMusicList();
      }

      switchVisualizerMode(mode) {
        this.visualizerMode = mode;
        this.vizModeButtons.forEach(btn => {
          btn.classList.toggle('active', btn.dataset.mode === mode);
        });
      }

      showMessage(message) {
        // Create a temporary message display
        const messageEl = document.createElement('div');
        messageEl.textContent = message;
        messageEl.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: var(--bg-panel);
          color: var(--primary);
          padding: 10px 20px;
          border-radius: 8px;
          border: 1px solid var(--primary);
          box-shadow: var(--border-glow);
          z-index: 1000;
          font-family: 'Orbitron', monospace;
          font-size: 12px;
        `;

        document.body.appendChild(messageEl);

        setTimeout(() => {
          document.body.removeChild(messageEl);
        }, 3000);
      }

      showHelp() {
        const helpModal = document.createElement('div');
        helpModal.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2000;
          font-family: 'Orbitron', monospace;
        `;

        helpModal.innerHTML = `
          <div style="
            background: var(--bg-panel);
            border: 2px solid var(--primary);
            border-radius: 12px;
            padding: 30px;
            max-width: 600px;
            box-shadow: var(--border-glow);
            color: #e0e0e0;
          ">
            <h2 style="color: var(--primary); text-shadow: var(--text-glow); margin-bottom: 20px; text-align: center;">
              🎵 How to Load Your Music
            </h2>
            <div style="line-height: 1.6; font-size: 14px;">
              <p style="margin-bottom: 15px;"><strong style="color: var(--accent);">Step 1:</strong> Click "📁 Load Your MUSIC Folder" button above</p>
              <p style="margin-bottom: 15px;"><strong style="color: var(--accent);">Step 2:</strong> In the file dialog, navigate to: <code style="background: rgba(0,0,0,0.5); padding: 2px 6px; border-radius: 4px;">C:\\Users\\<USER>\\Desktop\\PLAYER MP3\\</code></p>
              <p style="margin-bottom: 15px;"><strong style="color: var(--accent);">Step 3:</strong> Click on the <strong>MUSIC</strong> folder to select it</p>
              <p style="margin-bottom: 15px;"><strong style="color: var(--accent);">Step 4:</strong> Click "Select Folder" or "Open" button</p>
              <p style="margin-bottom: 15px; color: var(--secondary);"><strong>⚠️ Important:</strong> You must select the entire MUSIC folder, not individual files!</p>
              <hr style="border: 1px solid rgba(0, 212, 255, 0.3); margin: 20px 0;">
              <p style="margin-bottom: 10px; color: var(--secondary);"><strong>Your Current Music:</strong></p>
              <ul style="margin-left: 20px; margin-bottom: 15px;">
                <li>🎤 Linkin Park - Hybrid Theory (12 tracks)</li>
                <li>🖼️ Album art: animated hybrid theory art.gif</li>
              </ul>
              <p style="color: var(--success); font-size: 12px;">
                ✅ The player will automatically organize your music by Artist → Album → Songs<br>
                ✅ Album art will be loaded from your "Album Art" folders<br>
                ✅ All your existing music structure will be preserved
              </p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" style="
              background: var(--primary);
              color: #000;
              border: none;
              padding: 10px 20px;
              border-radius: 6px;
              cursor: pointer;
              font-weight: 600;
              margin-top: 20px;
              width: 100%;
              font-family: 'Orbitron', monospace;
            ">Got it! 🎵</button>
          </div>
        `;

        document.body.appendChild(helpModal);

        // Close on background click
        helpModal.addEventListener('click', (e) => {
          if (e.target === helpModal) {
            document.body.removeChild(helpModal);
          }
        });
      }

      testUpload() {
        const testModal = document.createElement('div');
        testModal.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2000;
          font-family: 'Orbitron', monospace;
        `;

        testModal.innerHTML = `
          <div style="
            background: var(--bg-panel);
            border: 2px solid var(--primary);
            border-radius: 12px;
            padding: 30px;
            max-width: 600px;
            box-shadow: var(--border-glow);
            color: #e0e0e0;
          ">
            <h2 style="color: var(--primary); text-shadow: var(--text-glow); margin-bottom: 20px; text-align: center;">
              🔧 Upload Test & Troubleshooting
            </h2>
            <div style="line-height: 1.6; font-size: 14px;">
              <p style="margin-bottom: 15px;"><strong style="color: var(--accent);">Browser Support Check:</strong></p>
              <p style="margin-bottom: 10px;">✅ webkitdirectory supported: ${document.createElement('input').webkitdirectory !== undefined}</p>
              <p style="margin-bottom: 15px;">✅ File API supported: ${window.File && window.FileReader && window.FileList && window.Blob}</p>

              <p style="margin-bottom: 15px;"><strong style="color: var(--accent);">Alternative Methods:</strong></p>
              <p style="margin-bottom: 10px;">1. Try using <strong>Chrome</strong> or <strong>Edge</strong> browser</p>
              <p style="margin-bottom: 10px;">2. Make sure you're selecting the <strong>folder</strong>, not files</p>
              <p style="margin-bottom: 10px;">3. Try selecting a smaller folder first to test</p>

              <div style="margin: 20px 0; padding: 15px; background: rgba(255, 107, 53, 0.1); border-left: 3px solid var(--secondary); border-radius: 4px;">
                <p style="color: var(--secondary); font-weight: 600;">Quick Test:</p>
                <button onclick="document.getElementById('testFileInput').click()" style="
                  background: var(--bg-button);
                  color: var(--primary);
                  border: 1px solid var(--primary);
                  padding: 8px 16px;
                  border-radius: 4px;
                  cursor: pointer;
                  margin-top: 10px;
                ">📁 Test Folder Selection</button>
                <input type="file" id="testFileInput" webkitdirectory multiple style="display: none;">
                <div id="testResult" style="margin-top: 10px; font-size: 12px;"></div>
              </div>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" style="
              background: var(--primary);
              color: #000;
              border: none;
              padding: 10px 20px;
              border-radius: 6px;
              cursor: pointer;
              font-weight: 600;
              margin-top: 20px;
              width: 100%;
              font-family: 'Orbitron', monospace;
            ">Close</button>
          </div>
        `;

        document.body.appendChild(testModal);

        // Add test functionality
        const testInput = testModal.querySelector('#testFileInput');
        const testResult = testModal.querySelector('#testResult');

        testInput.addEventListener('change', (e) => {
          const files = Array.from(e.target.files);
          testResult.innerHTML = `
            <p style="color: var(--success);">✅ Files selected: ${files.length}</p>
            <p style="color: var(--accent);">Audio files: ${files.filter(f => f.type.startsWith('audio')).length}</p>
            <p style="color: var(--accent);">Image files: ${files.filter(f => f.type.startsWith('image')).length}</p>
          `;
        });

        // Close on background click
        testModal.addEventListener('click', (e) => {
          if (e.target === testModal) {
            document.body.removeChild(testModal);
          }
        });
      }

      // === VISUALIZER FUNCTIONALITY ===
      setupVisualizer() {
        this.visualizerCanvas.width = this.visualizerCanvas.clientWidth;
        this.visualizerCanvas.height = this.visualizerCanvas.clientHeight;
        this.ctx = this.visualizerCanvas.getContext('2d');

        // Resize canvas when window resizes
        window.addEventListener('resize', () => {
          this.visualizerCanvas.width = this.visualizerCanvas.clientWidth;
          this.visualizerCanvas.height = this.visualizerCanvas.clientHeight;
        });

        this.startVisualizerLoop();
      }

      initAudioContext() {
        if (this.audioContext) return;

        try {
          this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
          this.analyser = this.audioContext.createAnalyser();
          this.analyser.fftSize = 256;
          this.bufferLength = this.analyser.frequencyBinCount;
          this.dataArray = new Uint8Array(this.bufferLength);

          this.sourceNode = this.audioContext.createMediaElementSource(this.audioPlayer);
          this.sourceNode.connect(this.analyser);
          this.analyser.connect(this.audioContext.destination);
        } catch (e) {
          console.error('Error initializing audio context:', e);
        }
      }

      startVisualizerLoop() {
        const animate = () => {
          requestAnimationFrame(animate);
          this.drawVisualizer();
        };
        animate();
      }

      drawVisualizer() {
        const canvas = this.visualizerCanvas;
        const ctx = this.ctx;

        if (!canvas || !ctx) return;

        // Clear canvas
        ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        if (!this.analyser || !this.isPlaying) {
          this.drawIdleVisualizer();
          return;
        }

        this.analyser.getByteFrequencyData(this.dataArray);

        switch (this.visualizerMode) {
          case 'spectrum':
            this.drawSpectrum();
            break;
          case 'waveform':
            this.drawWaveform();
            break;
          case 'circular':
            this.drawCircular();
            break;
        }
      }

      drawIdleVisualizer() {
        const ctx = this.ctx;
        const canvas = this.visualizerCanvas;
        const time = Date.now() * 0.001;

        // Draw animated idle pattern
        ctx.strokeStyle = 'rgba(0, 212, 255, 0.3)';
        ctx.lineWidth = 2;
        ctx.beginPath();

        for (let i = 0; i < canvas.width; i += 10) {
          const y = canvas.height / 2 + Math.sin(time + i * 0.01) * 20;
          if (i === 0) ctx.moveTo(i, y);
          else ctx.lineTo(i, y);
        }

        ctx.stroke();
      }

      drawSpectrum() {
        const ctx = this.ctx;
        const canvas = this.visualizerCanvas;
        const barWidth = (canvas.width / this.bufferLength) * 2.5;
        let x = 0;

        for (let i = 0; i < this.bufferLength; i++) {
          const barHeight = (this.dataArray[i] / 255) * canvas.height * 0.8;

          // Create gradient
          const gradient = ctx.createLinearGradient(0, canvas.height, 0, canvas.height - barHeight);
          gradient.addColorStop(0, '#00d4ff');
          gradient.addColorStop(0.5, '#ff6b35');
          gradient.addColorStop(1, '#ffd700');

          ctx.fillStyle = gradient;
          ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

          x += barWidth + 1;
        }
      }

      drawWaveform() {
        const ctx = this.ctx;
        const canvas = this.visualizerCanvas;

        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 3;
        ctx.beginPath();

        const sliceWidth = canvas.width / this.bufferLength;
        let x = 0;

        for (let i = 0; i < this.bufferLength; i++) {
          const v = this.dataArray[i] / 128.0;
          const y = v * canvas.height / 2;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }

          x += sliceWidth;
        }

        ctx.stroke();
      }

      drawCircular() {
        const ctx = this.ctx;
        const canvas = this.visualizerCanvas;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) * 0.6;

        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 2;

        for (let i = 0; i < this.bufferLength; i++) {
          const angle = (i / this.bufferLength) * Math.PI * 2;
          const barHeight = (this.dataArray[i] / 255) * radius * 0.5;

          const x1 = centerX + Math.cos(angle) * radius;
          const y1 = centerY + Math.sin(angle) * radius;
          const x2 = centerX + Math.cos(angle) * (radius + barHeight);
          const y2 = centerY + Math.sin(angle) * (radius + barHeight);

          ctx.beginPath();
          ctx.moveTo(x1, y1);
          ctx.lineTo(x2, y2);
          ctx.stroke();
        }
      }

      updateEqualizer() {
        // Basic EQ implementation would go here
        // For now, just visual feedback
        console.log('Equalizer updated');
      }
    }

    // Initialize the music player when the page loads
    document.addEventListener('DOMContentLoaded', () => {
      window.musicPlayer = new MusicPlayer();
    });
  </script>
</body>
</html>