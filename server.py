#!/usr/bin/env python3
"""
Smart Music Server for Nexus Music Player
Automatically scans and serves music from the MUSIC folder
"""

import http.server
import socketserver
import os
import sys
import json
import mimetypes
from urllib.parse import urlparse, unquote
from pathlib import Path

class SmartMusicHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        # Handle music library API endpoint
        if self.path == '/api/music-library':
            self.serve_music_library()
        else:
            super().do_GET()

    def serve_music_library(self):
        """Scan MUSIC folder and return JSON with all music files"""
        try:
            music_data = self.scan_music_folder()

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            response = json.dumps(music_data, indent=2)
            self.wfile.write(response.encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            error_response = json.dumps({'error': str(e)})
            self.wfile.write(error_response.encode('utf-8'))

    def scan_music_folder(self):
        """Scan the MUSIC folder and extract all music files with metadata"""
        music_folder = Path('MUSIC')

        if not music_folder.exists():
            return {'tracks': [], 'artists': [], 'albums': [], 'albumArt': []}

        tracks = []
        artists = {}
        albums = {}
        album_art = []

        # Supported audio formats
        audio_extensions = {'.mp3', '.m4a', '.wav', '.ogg', '.flac', '.aac'}
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}

        # Walk through all files in MUSIC folder
        for file_path in music_folder.rglob('*'):
            if file_path.is_file():
                extension = file_path.suffix.lower()

                # Process audio files
                if extension in audio_extensions:
                    track_data = self.extract_track_metadata(file_path)
                    tracks.append(track_data)

                    # Group by artist
                    artist_name = track_data['artist']
                    if artist_name not in artists:
                        artists[artist_name] = {
                            'name': artist_name,
                            'tracks': [],
                            'albums': set()
                        }
                    artists[artist_name]['tracks'].append(track_data['id'])
                    artists[artist_name]['albums'].add(track_data['album'])

                    # Group by album
                    album_key = f"{artist_name}::{track_data['album']}"
                    if album_key not in albums:
                        albums[album_key] = {
                            'title': track_data['album'],
                            'artist': artist_name,
                            'tracks': [],
                            'year': track_data.get('year'),
                            'genre': track_data.get('genre')
                        }
                    albums[album_key]['tracks'].append(track_data['id'])

                # Process album art
                elif extension in image_extensions:
                    art_data = self.extract_album_art_metadata(file_path)
                    album_art.append(art_data)

        # Convert sets to lists for JSON serialization
        for artist_data in artists.values():
            artist_data['albums'] = list(artist_data['albums'])

        return {
            'tracks': tracks,
            'artists': list(artists.values()),
            'albums': list(albums.values()),
            'albumArt': album_art,
            'stats': {
                'totalTracks': len(tracks),
                'totalArtists': len(artists),
                'totalAlbums': len(albums)
            }
        }

    def extract_track_metadata(self, file_path):
        """Extract metadata from audio file path"""
        # Convert to relative path from server root
        relative_path = file_path.as_posix()

        # Generate unique ID
        track_id = str(hash(relative_path))

        # Extract metadata from path structure
        parts = file_path.parts
        filename = file_path.stem

        # Default values
        artist = 'Unknown Artist'
        album = 'Unknown Album'
        title = filename

        # Try to extract from path: MUSIC/ARTISTS/Artist/Albums/Album/track.mp3
        if len(parts) >= 6 and parts[1] == 'ARTISTS':
            artist = parts[2]
            if len(parts) >= 6 and parts[4] == 'Albums':
                album = parts[5]  # This should be "Hybrid Theory"

        # Clean up track number from title
        if title and title[0].isdigit():
            # Remove track number prefix like "01 - " or "1. "
            import re
            title = re.sub(r'^\d+[\s\-\.]*', '', title)

        # Get file stats
        file_stats = file_path.stat()

        return {
            'id': track_id,
            'title': title,
            'artist': artist,
            'album': album,
            'genre': 'Rock',  # Default genre, could be enhanced
            'year': None,
            'duration': 0,  # Would need audio library to get actual duration
            'trackNumber': None,
            'diskNumber': None,
            'size': file_stats.st_size,
            'format': mimetypes.guess_type(str(file_path))[0] or 'audio/mpeg',
            'path': relative_path,
            'url': f'/{relative_path}',
            'dateAdded': int(file_stats.st_mtime * 1000),
            'playCount': 0,
            'lastPlayed': None
        }

    def extract_album_art_metadata(self, file_path):
        """Extract album art metadata"""
        relative_path = file_path.as_posix()
        parts = file_path.parts

        # Try to determine artist from path
        artist = 'Unknown Artist'
        if len(parts) >= 3 and parts[1] == 'ARTISTS':
            artist = parts[2]

        return {
            'path': relative_path,
            'url': f'/{relative_path}',
            'artist': artist,
            'filename': file_path.name
        }

def run_server(port=8000):
    """Run the HTTP server"""
    try:
        with socketserver.TCPServer(("", port), SmartMusicHandler) as httpd:
            print(f"🎵 Nexus Music Player Server")
            print(f"📡 Serving at http://localhost:{port}")
            print(f"🌐 Open http://localhost:{port} in your browser")
            print(f"⏹️  Press Ctrl+C to stop")
            print("-" * 50)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {port} is already in use. Trying port {port + 1}...")
            run_server(port + 1)
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Get port from command line argument or use default
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8000.")
    
    run_server(port)
