#!/usr/bin/env python3
"""
Smart Music Server for Nexus Music Player
Automatically scans and serves music from the MUSIC folder
"""

import http.server
import socketserver
import os
import sys
import json
import mimetypes
import base64
import hashlib
from urllib.parse import urlparse, unquote
from pathlib import Path
from mutagen import File as MutagenFile python server.py
from mutagen.id3 import ID3NoHeaderError
import io

class SmartMusicHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        # Handle music library API endpoint
        if self.path == '/api/music-library':
            self.serve_music_library()
        # Handle album art requests
        elif self.path.startswith('/api/album-art/'):
            self.serve_album_art()
        else:
            super().do_GET()

    def serve_music_library(self):
        """Scan MUSIC folder and return JSON with all music files"""
        try:
            music_data = self.scan_music_folder()

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            response = json.dumps(music_data, indent=2)
            self.wfile.write(response.encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            error_response = json.dumps({'error': str(e)})
            self.wfile.write(error_response.encode('utf-8'))

    def scan_music_folder(self):
        """Scan the MUSIC folder and extract all music files with metadata"""
        music_folder = Path('MUSIC')

        if not music_folder.exists():
            return {'tracks': [], 'artists': [], 'albums': [], 'albumArt': []}

        tracks = []
        artists = {}
        albums = {}
        album_art = []

        # Supported audio formats
        audio_extensions = {'.mp3', '.m4a', '.wav', '.ogg', '.flac', '.aac'}
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}

        # Walk through all files in MUSIC folder
        for file_path in music_folder.rglob('*'):
            if file_path.is_file():
                extension = file_path.suffix.lower()

                # Process audio files
                if extension in audio_extensions:
                    track_data = self.extract_track_metadata(file_path)
                    if track_data:  # Only add if metadata extraction succeeded
                        tracks.append(track_data)

                        # Group by artist
                        artist_name = track_data['artist']
                        if artist_name not in artists:
                            artists[artist_name] = {
                                'name': artist_name,
                                'tracks': [],
                                'albums': set()
                            }
                        artists[artist_name]['tracks'].append(track_data['id'])
                        artists[artist_name]['albums'].add(track_data['album'])

                        # Group by album
                        album_key = f"{artist_name}::{track_data['album']}"
                        if album_key not in albums:
                            albums[album_key] = {
                                'title': track_data['album'],
                                'artist': artist_name,
                                'tracks': [],
                                'year': track_data.get('year'),
                                'genre': track_data.get('genre')
                            }
                        albums[album_key]['tracks'].append(track_data['id'])

                # Process album art
                elif extension in image_extensions:
                    art_data = self.extract_album_art_metadata(file_path)
                    album_art.append(art_data)

        # Convert sets to lists for JSON serialization
        for artist_data in artists.values():
            artist_data['albums'] = list(artist_data['albums'])

        return {
            'tracks': tracks,
            'artists': list(artists.values()),
            'albums': list(albums.values()),
            'albumArt': album_art,
            'stats': {
                'totalTracks': len(tracks),
                'totalArtists': len(artists),
                'totalAlbums': len(albums)
            }
        }

    def extract_track_metadata(self, file_path):
        """Extract metadata from audio file using mutagen"""
        try:
            # Convert to relative path from server root
            relative_path = file_path.as_posix()

            # Generate unique ID
            track_id = str(abs(hash(relative_path)))

            # Extract metadata from path structure as fallback
            parts = file_path.parts
            filename = file_path.stem

            # Default values from path
            path_artist = 'Unknown Artist'
            path_album = 'Unknown Album'
            path_title = filename

            # Try to extract from path: MUSIC/ARTISTS/Artist/Albums/Album/track.mp3
            if len(parts) >= 6 and parts[1] == 'ARTISTS':
                path_artist = parts[2]
                if len(parts) >= 6 and parts[4] == 'Albums':
                    path_album = parts[5]

            # Clean up track number from title
            import re
            path_title = re.sub(r'^\d+[\s\-\.]*', '', path_title)

            # Try to read metadata from file
            audio_file = MutagenFile(str(file_path))

            # Initialize with path-based defaults
            title = path_title
            artist = path_artist
            album = path_album
            genre = None
            year = None
            track_number = None
            duration = 0

            if audio_file is not None:
                # Extract metadata based on file type
                if hasattr(audio_file, 'info') and audio_file.info:
                    duration = int(audio_file.info.length) if audio_file.info.length else 0

                # Handle ID3 tags (MP3)
                if hasattr(audio_file, 'tags') and audio_file.tags:
                    tags = audio_file.tags

                    # Title
                    if 'TIT2' in tags:
                        title = str(tags['TIT2'][0])
                    elif 'TITLE' in tags:
                        title = str(tags['TITLE'][0])

                    # Artist
                    if 'TPE1' in tags:
                        artist = str(tags['TPE1'][0])
                    elif 'ARTIST' in tags:
                        artist = str(tags['ARTIST'][0])

                    # Album
                    if 'TALB' in tags:
                        album = str(tags['TALB'][0])
                    elif 'ALBUM' in tags:
                        album = str(tags['ALBUM'][0])

                    # Genre
                    if 'TCON' in tags:
                        genre = str(tags['TCON'][0])
                    elif 'GENRE' in tags:
                        genre = str(tags['GENRE'][0])

                    # Year
                    if 'TDRC' in tags:
                        year = str(tags['TDRC'][0])[:4]  # Extract year part
                    elif 'TYER' in tags:
                        year = str(tags['TYER'][0])
                    elif 'DATE' in tags:
                        year = str(tags['DATE'][0])[:4]

                    # Track number
                    if 'TRCK' in tags:
                        track_str = str(tags['TRCK'][0])
                        track_number = int(track_str.split('/')[0]) if '/' in track_str else int(track_str)
                    elif 'TRACKNUMBER' in tags:
                        track_number = int(str(tags['TRACKNUMBER'][0]))

            # Get file stats
            file_stats = file_path.stat()



            return {
                'id': track_id,
                'title': title or path_title,
                'artist': artist or path_artist,
                'album': album or path_album,
                'genre': genre,
                'year': year,
                'duration': duration,
                'trackNumber': track_number,
                'diskNumber': None,
                'size': file_stats.st_size,
                'format': mimetypes.guess_type(str(file_path))[0] or 'audio/mpeg',
                'path': relative_path,
                'url': f'/{relative_path}',
                'dateAdded': int(file_stats.st_mtime * 1000),
                'playCount': 0,
                'lastPlayed': None,
                'albumArt': self.find_album_art(file_path)
            }

        except Exception as e:
            print(f"Error extracting metadata from {file_path}: {e}")
            return None

    def find_album_art(self, audio_file_path):
        """Find album art for a given audio file"""
        # First, try to extract embedded album art
        try:
            audio_file = MutagenFile(str(audio_file_path))
            if audio_file and hasattr(audio_file, 'tags') and audio_file.tags:
                # Look for embedded artwork in ID3 tags
                if 'APIC:' in audio_file.tags:
                    artwork = audio_file.tags['APIC:']
                    return {
                        'type': 'embedded',
                        'data': base64.b64encode(artwork.data).decode('utf-8'),
                        'mime': artwork.mime
                    }
        except:
            pass

        # Look for album art files in the same directory and parent directories
        current_dir = audio_file_path.parent
        art_filenames = ['cover.jpg', 'cover.png', 'folder.jpg', 'folder.png',
                        'album.jpg', 'album.png', 'artwork.jpg', 'artwork.png']

        # Check current directory (album folder)
        for art_name in art_filenames:
            art_path = current_dir / art_name
            if art_path.exists():
                return {
                    'type': 'file',
                    'url': f'/{art_path.as_posix()}',
                    'path': art_path.as_posix()
                }

        # Check parent directory (artist folder) for Album Art folder
        parent_dir = current_dir.parent
        if parent_dir.name != 'MUSIC':
            album_art_dir = parent_dir / 'Album Art'
            if album_art_dir.exists():
                for art_file in album_art_dir.iterdir():
                    if art_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                        return {
                            'type': 'file',
                            'url': f'/{art_file.as_posix()}',
                            'path': art_file.as_posix()
                        }

        return None

    def serve_album_art(self):
        """Serve album art for a specific track"""
        try:
            # Extract track ID from path: /api/album-art/{track_id}
            track_id = self.path.split('/')[-1]

            # Find the track file (this is a simplified approach)
            music_folder = Path('MUSIC')
            for file_path in music_folder.rglob('*.mp3'):
                if str(abs(hash(file_path.as_posix()))) == track_id:
                    album_art = self.find_album_art(file_path)

                    if album_art:
                        if album_art['type'] == 'embedded':
                            # Serve embedded artwork
                            self.send_response(200)
                            self.send_header('Content-type', album_art['mime'])
                            self.send_header('Cache-Control', 'max-age=3600')
                            self.end_headers()

                            artwork_data = base64.b64decode(album_art['data'])
                            self.wfile.write(artwork_data)
                            return

                        elif album_art['type'] == 'file':
                            # Serve file-based artwork
                            art_path = Path(album_art['path'])
                            if art_path.exists():
                                self.send_response(200)
                                mime_type = mimetypes.guess_type(str(art_path))[0] or 'image/jpeg'
                                self.send_header('Content-type', mime_type)
                                self.send_header('Cache-Control', 'max-age=3600')
                                self.end_headers()

                                with open(art_path, 'rb') as f:
                                    self.wfile.write(f.read())
                                return

            # No album art found
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{"error": "Album art not found"}')

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            error_response = json.dumps({'error': f'Failed to serve album art: {str(e)}'})
            self.wfile.write(error_response.encode('utf-8'))

    def extract_album_art_metadata(self, file_path):
        """Extract album art metadata"""
        relative_path = file_path.as_posix()
        parts = file_path.parts

        # Try to determine artist from path
        artist = 'Unknown Artist'
        if len(parts) >= 3 and parts[1] == 'ARTISTS':
            artist = parts[2]

        return {
            'path': relative_path,
            'url': f'/{relative_path}',
            'artist': artist,
            'filename': file_path.name
        }

def run_server(port=8000):
    """Run the HTTP server"""
    try:
        with socketserver.TCPServer(("", port), SmartMusicHandler) as httpd:
            print(f"🎵 Nexus Music Player Server")
            print(f"📡 Serving at http://localhost:{port}")
            print(f"🌐 Open http://localhost:{port} in your browser")
            print(f"⏹️  Press Ctrl+C to stop")
            print("-" * 50)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {port} is already in use. Trying port {port + 1}...")
            run_server(port + 1)
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Get port from command line argument or use default
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8000.")
    
    run_server(port)
