// ===== ADVANCED AUDIO VISUALIZER =====

import { APP_CONFIG, VISUALIZER_CONFIGS } from '../utils/constants.js';
import { MathUtils, DOMUtils } from '../utils/helpers.js';

export class Visualizer {
    constructor(audioEngine, canvasElement) {
        this.audioEngine = audioEngine;
        this.canvas = canvasElement;
        this.ctx = this.canvas.getContext('2d');
        
        // Visualizer state
        this.mode = 'bars';
        this.isActive = false;
        this.animationId = null;
        
        // Data arrays
        this.frequencyData = null;
        this.timeData = null;
        this.previousData = null;
        
        // Visual properties
        this.colors = {
            primary: '#6366f1',
            secondary: '#8b5cf6',
            accent: '#f59e0b',
            background: 'rgba(15, 15, 15, 0.1)'
        };
        
        // Particle system for particle mode
        this.particles = [];
        
        // Performance tracking
        this.frameCount = 0;
        this.lastFrameTime = 0;
        this.fps = 0;
        
        this.initialize();
    }

    /**
     * Initialize visualizer
     */
    initialize() {
        this.setupCanvas();
        this.setupEventListeners();
        this.start();
        
        console.log('Visualizer initialized');
    }

    /**
     * Setup canvas properties
     */
    setupCanvas() {
        this.resizeCanvas();
        
        // Set canvas properties for better rendering
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Resize canvas when window resizes
        window.addEventListener('resize', DOMUtils.debounce(() => {
            this.resizeCanvas();
        }, 250));
        
        // Audio engine events
        this.audioEngine.addEventListener('play', () => {
            this.isActive = true;
        });
        
        this.audioEngine.addEventListener('pause', () => {
            this.isActive = false;
        });
        
        this.audioEngine.addEventListener('ended', () => {
            this.isActive = false;
        });
    }

    /**
     * Resize canvas to match container
     */
    resizeCanvas() {
        const rect = this.canvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.ctx.scale(dpr, dpr);
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }

    /**
     * Start visualization loop
     */
    start() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        this.animate();
    }

    /**
     * Stop visualization
     */
    stop() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    /**
     * Main animation loop
     */
    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());
        
        // Calculate FPS
        const now = performance.now();
        if (this.lastFrameTime) {
            const delta = now - this.lastFrameTime;
            this.fps = 1000 / delta;
        }
        this.lastFrameTime = now;
        this.frameCount++;
        
        // Clear canvas
        this.clearCanvas();
        
        if (this.isActive && this.audioEngine.isInitialized) {
            // Get audio data
            this.frequencyData = this.audioEngine.getFrequencyData();
            this.timeData = this.audioEngine.getTimeData();
            
            if (this.frequencyData) {
                // Render based on current mode
                this.render();
            }
        } else {
            // Render idle animation
            this.renderIdle();
        }
    }

    /**
     * Clear canvas with background
     */
    clearCanvas() {
        const rect = this.canvas.getBoundingClientRect();
        
        // Create gradient background
        const gradient = this.ctx.createRadialGradient(
            rect.width / 2, rect.height / 2, 0,
            rect.width / 2, rect.height / 2, Math.max(rect.width, rect.height) / 2
        );
        gradient.addColorStop(0, 'rgba(99, 102, 241, 0.05)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.9)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, rect.width, rect.height);
    }

    /**
     * Main render function
     */
    render() {
        switch (this.mode) {
            case 'bars':
                this.renderBars();
                break;
            case 'wave':
                this.renderWave();
                break;
            case 'circle':
                this.renderCircle();
                break;
            case 'particles':
                this.renderParticles();
                break;
            default:
                this.renderBars();
        }
    }

    /**
     * Render bar visualizer
     */
    renderBars() {
        const config = VISUALIZER_CONFIGS.bars;
        const rect = this.canvas.getBoundingClientRect();
        const barCount = Math.min(config.barCount, this.frequencyData.length);
        const barWidth = (rect.width - (barCount - 1) * config.barSpacing) / barCount;
        
        for (let i = 0; i < barCount; i++) {
            const dataIndex = Math.floor((i / barCount) * this.frequencyData.length);
            const value = this.frequencyData[dataIndex] / 255;
            const barHeight = value * rect.height * config.amplification;
            
            const x = i * (barWidth + config.barSpacing);
            const y = rect.height - barHeight;
            
            // Create gradient for each bar
            const gradient = this.ctx.createLinearGradient(0, rect.height, 0, y);
            gradient.addColorStop(0, this.colors.primary);
            gradient.addColorStop(0.5, this.colors.secondary);
            gradient.addColorStop(1, this.colors.accent);
            
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(x, y, barWidth, barHeight);
            
            // Add glow effect
            this.ctx.shadowColor = this.colors.primary;
            this.ctx.shadowBlur = 10;
            this.ctx.fillRect(x, y, barWidth, barHeight);
            this.ctx.shadowBlur = 0;
        }
    }

    /**
     * Render waveform visualizer
     */
    renderWave() {
        const config = VISUALIZER_CONFIGS.wave;
        const rect = this.canvas.getBoundingClientRect();
        const centerY = rect.height / 2;
        
        this.ctx.beginPath();
        this.ctx.lineWidth = config.lineWidth;
        this.ctx.strokeStyle = this.colors.primary;
        
        const sliceWidth = rect.width / this.timeData.length;
        let x = 0;
        
        for (let i = 0; i < this.timeData.length; i++) {
            const value = (this.timeData[i] - 128) / 128;
            const y = centerY + (value * centerY * config.amplification);
            
            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
            
            x += sliceWidth;
        }
        
        this.ctx.stroke();
        
        // Add glow effect
        this.ctx.shadowColor = this.colors.primary;
        this.ctx.shadowBlur = 15;
        this.ctx.stroke();
        this.ctx.shadowBlur = 0;
    }

    /**
     * Render circular visualizer
     */
    renderCircle() {
        const config = VISUALIZER_CONFIGS.circle;
        const rect = this.canvas.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        const radius = Math.min(centerX, centerY) * 0.6;
        
        this.ctx.lineWidth = config.lineWidth;
        this.ctx.strokeStyle = this.colors.primary;
        
        const angleStep = (Math.PI * 2) / this.frequencyData.length;
        let rotation = config.rotation ? (this.frameCount * 0.01) : 0;
        
        for (let i = 0; i < this.frequencyData.length; i++) {
            const value = this.frequencyData[i] / 255;
            const angle = i * angleStep + rotation;
            const barLength = value * radius * config.amplification;
            
            const x1 = centerX + Math.cos(angle) * radius;
            const y1 = centerY + Math.sin(angle) * radius;
            const x2 = centerX + Math.cos(angle) * (radius + barLength);
            const y2 = centerY + Math.sin(angle) * (radius + barLength);
            
            // Color based on frequency
            const hue = (i / this.frequencyData.length) * 360;
            this.ctx.strokeStyle = `hsl(${hue}, 70%, 60%)`;
            
            this.ctx.beginPath();
            this.ctx.moveTo(x1, y1);
            this.ctx.lineTo(x2, y2);
            this.ctx.stroke();
        }
    }

    /**
     * Render particle visualizer
     */
    renderParticles() {
        const config = VISUALIZER_CONFIGS.particles;
        const rect = this.canvas.getBoundingClientRect();
        
        // Update existing particles
        this.particles = this.particles.filter(particle => {
            particle.update();
            particle.draw(this.ctx);
            return particle.life > 0;
        });
        
        // Create new particles based on audio data
        if (this.frequencyData) {
            const bassLevel = this.getAverageFrequency(0, 10);
            const midLevel = this.getAverageFrequency(10, 50);
            const trebleLevel = this.getAverageFrequency(50, 100);
            
            // Create particles based on frequency levels
            if (bassLevel > 0.3) {
                this.createParticles(rect.width / 2, rect.height / 2, bassLevel, this.colors.primary);
            }
            if (midLevel > 0.3) {
                this.createParticles(rect.width * 0.25, rect.height / 2, midLevel, this.colors.secondary);
            }
            if (trebleLevel > 0.3) {
                this.createParticles(rect.width * 0.75, rect.height / 2, trebleLevel, this.colors.accent);
            }
        }
    }

    /**
     * Create particles at position
     */
    createParticles(x, y, intensity, color) {
        const config = VISUALIZER_CONFIGS.particles;
        const count = Math.floor(intensity * 10);
        
        for (let i = 0; i < count; i++) {
            this.particles.push(new Particle(x, y, intensity, color, config));
        }
    }

    /**
     * Get average frequency in range
     */
    getAverageFrequency(startIndex, endIndex) {
        if (!this.frequencyData) return 0;
        
        let sum = 0;
        const count = endIndex - startIndex;
        
        for (let i = startIndex; i < endIndex && i < this.frequencyData.length; i++) {
            sum += this.frequencyData[i];
        }
        
        return (sum / count) / 255;
    }

    /**
     * Render idle animation when no audio is playing
     */
    renderIdle() {
        const rect = this.canvas.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        const time = this.frameCount * 0.02;
        
        // Draw animated rings
        for (let i = 0; i < 3; i++) {
            const radius = 50 + i * 30 + Math.sin(time + i) * 10;
            const alpha = 0.3 - i * 0.1;
            
            this.ctx.beginPath();
            this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            this.ctx.strokeStyle = `rgba(99, 102, 241, ${alpha})`;
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
        }
        
        // Draw pulsing center
        const pulseRadius = 20 + Math.sin(time * 2) * 5;
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, pulseRadius, 0, Math.PI * 2);
        this.ctx.fillStyle = 'rgba(99, 102, 241, 0.5)';
        this.ctx.fill();
    }

    /**
     * Set visualizer mode
     */
    setMode(mode) {
        if (VISUALIZER_CONFIGS[mode]) {
            this.mode = mode;
            
            // Reset particles when switching to particle mode
            if (mode === 'particles') {
                this.particles = [];
            }
        }
    }

    /**
     * Set color scheme
     */
    setColors(colors) {
        Object.assign(this.colors, colors);
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        return {
            fps: Math.round(this.fps),
            frameCount: this.frameCount,
            particleCount: this.particles.length,
            mode: this.mode
        };
    }

    /**
     * Cleanup
     */
    destroy() {
        this.stop();
        this.particles = [];
        
        // Remove event listeners
        window.removeEventListener('resize', this.resizeCanvas);
    }
}

/**
 * Particle class for particle visualizer
 */
class Particle {
    constructor(x, y, intensity, color, config) {
        this.x = x;
        this.y = y;
        this.vx = (Math.random() - 0.5) * intensity * config.speed;
        this.vy = (Math.random() - 0.5) * intensity * config.speed;
        this.size = Math.random() * config.size * intensity;
        this.life = 1.0;
        this.decay = 0.02;
        this.color = color;
        this.gravity = config.gravity;
        this.bounce = config.bounce;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        
        // Bounce off edges
        if (this.x < 0 || this.x > window.innerWidth) {
            this.vx *= -this.bounce;
        }
        if (this.y < 0 || this.y > window.innerHeight) {
            this.vy *= -this.bounce;
        }
        
        this.life -= this.decay;
    }

    draw(ctx) {
        ctx.save();
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}
