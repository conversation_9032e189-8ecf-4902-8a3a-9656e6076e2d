/* Modern Music Player - WSOD Style */

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    margin-bottom: 30px;
}

.logo-text {
    font-size: 48px;
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -2px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--bg-tertiary);
    border-top: 3px solid var(--accent-orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    color: var(--text-secondary);
    font-size: 14px;
}

:root {
    --bg-primary: #000000;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --bg-card: #0f0f0f;
    --bg-hover: #222222;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
    --accent-primary: #ffffff;
    --accent-secondary: #333333;
    --border-color: #333333;
    --border-light: #444444;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
    --radius: 8px;
    --radius-lg: 12px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
}

.app-container {
    display: flex;
    height: 100vh;
    padding-bottom: 90px; /* Space for bottom player */
}

/* Sidebar */
.sidebar {
    width: 80px;
    background: var(--bg-secondary);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    border-right: 1px solid var(--border-color);
}

.logo {
    margin-bottom: 40px;
}

.logo-text {
    font-size: 18px;
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.5px;
}

.nav-menu {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.nav-item {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.nav-item.active {
    background: var(--text-primary);
    color: var(--bg-primary);
}

.nav-item:hover:not(.active) {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.user-avatar {
    margin-top: auto;
}

.user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
    background: var(--bg-primary);
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.header-tabs {
    display: flex;
    gap: 30px;
}

.tab-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 0;
    position: relative;
    transition: var(--transition);
}

.tab-btn.active {
    color: var(--text-primary);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 15px;
    color: var(--text-muted);
    font-size: 14px;
}

.search-box input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    padding: 10px 15px 10px 40px;
    color: var(--text-primary);
    font-size: 14px;
    width: 250px;
    outline: none;
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--accent-orange);
    box-shadow: 0 0 0 3px rgba(255, 140, 66, 0.1);
}

.notification-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
}

.notification-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Hero Section */
.hero-section {
    margin-bottom: 40px;
}

.hero-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 40px;
    position: relative;
    overflow: hidden;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    z-index: 2;
}

.hero-info {
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 8px;
    display: block;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin: 0;
    letter-spacing: -1px;
}

.play-btn-hero {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--text-primary);
    border: none;
    color: var(--bg-primary);
    font-size: 20px;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.play-btn-hero:hover {
    background: var(--text-secondary);
    transform: scale(1.05);
}

.hero-bg {
    display: none;
}

@keyframes heroFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-10px) scale(1.02);
    }
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-primary);
}

/* Track List */
.track-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.track-item {
    background: var(--bg-card);
    border-radius: var(--radius);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.track-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.track-item.featured {
    background: var(--bg-secondary);
    border: 1px solid var(--text-primary);
    color: var(--text-primary);
}

.track-item.playing {
    background: var(--bg-tertiary);
    border-color: var(--text-primary);
    box-shadow: 0 0 0 1px var(--text-primary);
}

.track-item.playing .track-name {
    color: var(--text-primary);
}

.track-artwork {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--text-secondary);
    overflow: hidden;
    position: relative;
}

.track-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.track-artwork i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.track-info {
    flex: 1;
}

.track-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
}

.track-artist {
    font-size: 14px;
    color: var(--text-secondary);
}

.track-duration {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Artist List */
.artist-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.artist-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.artist-item:hover {
    background: var(--bg-card);
}

.artist-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    color: var(--text-secondary);
}

.artist-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.artist-avatar i {
    font-size: 20px;
}

.artist-info {
    flex: 1;
}

.artist-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.artist-track-count {
    font-size: 12px;
    color: var(--text-secondary);
}

.artist-chevron {
    margin-left: auto;
    color: var(--text-secondary);
}

/* Empty states */
.no-tracks,
.no-artists {
    text-align: center;
    color: var(--text-secondary);
    padding: 40px 20px;
    font-style: italic;
}

/* Improve track list scrolling for many tracks */
.track-list {
    max-height: 500px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--text-primary) var(--bg-tertiary);
    padding-right: 8px;
}

.track-list::-webkit-scrollbar {
    width: 6px;
}

.track-list::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: 3px;
}

.track-list::-webkit-scrollbar-thumb {
    background: var(--text-secondary);
    border-radius: 3px;
}

.track-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary);
}

/* Right Sidebar */
.right-sidebar {
    width: 300px;
    background: var(--bg-secondary);
    padding: 20px;
    border-left: 1px solid var(--border-color);
    overflow-y: auto;
}

.widget {
    margin-bottom: 30px;
}

.widget-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.search-input {
    position: relative;
}

.search-input input {
    width: 100%;
    padding: 12px 40px 12px 16px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
    transition: var(--transition);
}

.search-input input:focus {
    border-color: var(--text-primary);
    background: var(--bg-primary);
}

.search-input i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 14px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 16px 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Search Results */
.no-tracks {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.no-tracks i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-tracks p {
    font-size: 16px;
    margin-bottom: 20px;
}

.clear-search-btn {
    background: var(--text-primary);
    color: var(--bg-primary);
    border: none;
    padding: 8px 16px;
    border-radius: var(--radius);
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.clear-search-btn:hover {
    background: var(--text-secondary);
}

/* Search Highlighting */
mark {
    background: var(--text-primary);
    color: var(--bg-primary);
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* Track Actions */
.track-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.favorite-btn,
.queue-add-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 14px;
    cursor: pointer;
    padding: 6px;
    border-radius: var(--radius);
    transition: var(--transition);
    opacity: 0;
}

.track-item:hover .favorite-btn,
.track-item:hover .queue-add-btn {
    opacity: 1;
}

.favorite-btn:hover,
.queue-add-btn:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
}

.favorite-btn.active {
    color: #ff4757;
    opacity: 1;
}

.favorite-btn.active:hover {
    color: #ff3838;
}

/* Queue Styles */
.queue-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 20px;
    margin-top: 20px;
}

.queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.queue-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.queue-clear-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 6px 12px;
    border-radius: var(--radius);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.queue-clear-btn:hover {
    border-color: var(--text-primary);
    color: var(--text-primary);
}

.queue-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-radius: var(--radius);
    margin-bottom: 8px;
    transition: var(--transition);
}

.queue-item:hover {
    background: var(--bg-tertiary);
}

.queue-item.current {
    background: var(--bg-tertiary);
    border: 1px solid var(--text-primary);
}

.queue-track-info {
    flex: 1;
}

.queue-track-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.queue-track-artist {
    font-size: 12px;
    color: var(--text-secondary);
}

.queue-actions {
    display: flex;
    gap: 8px;
}

.queue-play-btn,
.queue-remove-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius);
    transition: var(--transition);
}

.queue-play-btn:hover {
    color: var(--text-primary);
}

.queue-remove-btn:hover {
    color: #ff4757;
}

.empty-queue {
    text-align: center;
    color: var(--text-secondary);
    padding: 40px 20px;
    font-style: italic;
}

/* Speed Control */
.speed-control {
    margin-right: 8px;
}

.speed-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
}

#speed-btn:hover .speed-text {
    color: var(--text-primary);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius);
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
}

.modal-body {
    padding: 20px;
}

.setting-group {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.setting-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 12px;
}

.setting-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--text-primary);
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 24px;
}

.setting-control label {
    color: var(--text-secondary);
    font-size: 12px;
    min-width: 80px;
}

.setting-control input[type="range"] {
    flex: 1;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.setting-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: var(--text-primary);
    border-radius: 50%;
    cursor: pointer;
}

.setting-control input[type="range"]::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: var(--text-primary);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.widget {
    margin-bottom: 30px;
}

.widget-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

/* Tags */
.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.tag:hover {
    background: var(--bg-card);
}

.tag i {
    font-size: 10px;
    opacity: 0.7;
}

/* Podcast Grid */
.podcast-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.podcast-card {
    aspect-ratio: 1;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.podcast-card:hover {
    transform: scale(1.05);
}

.podcast-card.blue {
    background: linear-gradient(135deg, var(--accent-blue) 0%, #357abd 100%);
}

.podcast-card.purple {
    background: linear-gradient(135deg, var(--accent-purple) 0%, #7c3aed 100%);
}

.podcast-card.teal {
    background: linear-gradient(135deg, var(--accent-teal) 0%, #0d9488 100%);
}

/* Featured Podcast */
.featured-podcast {
    background: var(--bg-card);
    border-radius: var(--radius);
    padding: 16px;
}

.podcast-info {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.podcast-info img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
}

.podcast-details h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
}

.rating i {
    color: #fbbf24;
    font-size: 12px;
}

.rating span {
    font-size: 12px;
    color: var(--text-secondary);
    margin-left: 4px;
}

.podcast-details p {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.play-btn-small,
.remind-btn {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
}

.play-btn-small {
    background: var(--text-primary);
    color: var(--bg-primary);
}

.play-btn-small:hover {
    background: var(--text-secondary);
}

.remind-btn {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.remind-btn:hover {
    background: var(--bg-primary);
}

/* Bottom Player */
.bottom-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 90px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
}

.player-track-info {
    display: flex;
    align-items: center;
    gap: 16px;
    width: 300px;
}

.player-track-info img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: var(--bg-tertiary);
}

.track-details {
    flex: 1;
}

.track-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.track-artist {
    font-size: 12px;
    color: var(--text-secondary);
}

.player-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: center;
}

.control-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
}

.control-btn:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.control-btn.active {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.control-btn.repeat-one {
    position: relative;
}

.control-btn.repeat-one::after {
    content: '1';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    font-weight: bold;
    background: var(--text-primary);
    color: var(--bg-primary);
    border-radius: 50%;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn {
    width: 40px;
    height: 40px;
    background: var(--text-primary);
    color: var(--bg-primary);
    border-radius: 50%;
    font-size: 18px;
}

.play-btn:hover {
    background: var(--text-secondary);
    transform: scale(1.05);
}

.player-extras {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 300px;
    justify-content: flex-end;
}

.volume-slider {
    width: 100px;
}

.volume-slider input {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: var(--text-primary);
    border-radius: 50%;
    cursor: pointer;
}

.volume-slider input::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: var(--text-primary);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Progress Bar */
.progress-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    padding: 0 20px;
}

.time-current,
.time-total {
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 35px;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    margin: 0 12px;
}

.progress-fill {
    height: 100%;
    background: var(--text-primary);
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--text-primary);
    border-radius: 50%;
    transform: translate(50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .right-sidebar {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: 60px;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 20px;
    }

    .right-sidebar {
        display: none;
    }

    .header-actions .search-box input {
        width: 200px;
    }
}
