/* Modern Music Player - WSOD Style */

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    margin-bottom: 30px;
}

.logo-text {
    font-size: 48px;
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -2px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--bg-tertiary);
    border-top: 3px solid var(--accent-orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    color: var(--text-secondary);
    font-size: 14px;
}

:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #3a3a3a;
    --bg-card: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    --accent-orange: #ff8c42;
    --accent-blue: #4a90e2;
    --accent-purple: #8b5cf6;
    --accent-teal: #14b8a6;
    --border-color: #404040;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --radius: 12px;
    --radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
}

.app-container {
    display: flex;
    height: 100vh;
    padding-bottom: 90px; /* Space for bottom player */
}

/* Sidebar */
.sidebar {
    width: 80px;
    background: var(--bg-secondary);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    border-right: 1px solid var(--border-color);
}

.logo {
    margin-bottom: 40px;
}

.logo-text {
    font-size: 18px;
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.5px;
}

.nav-menu {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.nav-item {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.nav-item.active {
    background: var(--accent-orange);
    color: white;
}

.nav-item:hover:not(.active) {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.user-avatar {
    margin-top: auto;
}

.user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
    background: var(--bg-primary);
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.header-tabs {
    display: flex;
    gap: 30px;
}

.tab-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 0;
    position: relative;
    transition: var(--transition);
}

.tab-btn.active {
    color: var(--text-primary);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-orange);
    border-radius: 1px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 15px;
    color: var(--text-muted);
    font-size: 14px;
}

.search-box input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    padding: 10px 15px 10px 40px;
    color: var(--text-primary);
    font-size: 14px;
    width: 250px;
    outline: none;
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--accent-orange);
    box-shadow: 0 0 0 3px rgba(255, 140, 66, 0.1);
}

.notification-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
}

.notification-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Hero Section */
.hero-section {
    margin-bottom: 40px;
}

.hero-card {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%);
    border-radius: var(--radius-lg);
    padding: 40px;
    position: relative;
    overflow: hidden;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    z-index: 2;
}

.hero-info {
    color: white;
}

.hero-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 8px;
    display: block;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin: 0;
    letter-spacing: -1px;
}

.play-btn-hero {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.play-btn-hero:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.hero-bg {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: url('https://via.placeholder.com/400x200/ff8c42/ffffff?text=Music') center/cover;
    opacity: 0.3;
    animation: heroFloat 6s ease-in-out infinite;
}

@keyframes heroFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-10px) scale(1.02);
    }
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-primary);
}

/* Track List */
.track-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.track-item {
    background: var(--bg-card);
    border-radius: var(--radius);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.track-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.track-item.featured {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #ff6b35 100%);
    color: white;
}

.track-item.playing {
    background: var(--bg-tertiary);
    border-color: var(--accent-orange);
    box-shadow: 0 0 0 1px var(--accent-orange);
}

.track-item.playing .track-name {
    color: var(--accent-orange);
}

.track-artwork {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--text-secondary);
    overflow: hidden;
    position: relative;
}

.track-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.track-artwork i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.track-info {
    flex: 1;
}

.track-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
}

.track-artist {
    font-size: 14px;
    color: var(--text-secondary);
}

.track-duration {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Artist List */
.artist-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.artist-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.artist-item:hover {
    background: var(--bg-card);
}

.artist-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    color: var(--text-secondary);
}

.artist-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.artist-avatar i {
    font-size: 20px;
}

.artist-info {
    flex: 1;
}

.artist-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.artist-track-count {
    font-size: 12px;
    color: var(--text-secondary);
}

.artist-chevron {
    margin-left: auto;
    color: var(--text-secondary);
}

/* Empty states */
.no-tracks,
.no-artists {
    text-align: center;
    color: var(--text-secondary);
    padding: 40px 20px;
    font-style: italic;
}

/* Improve track list scrolling for many tracks */
.track-list {
    max-height: 600px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--accent-orange) var(--bg-tertiary);
}

.track-list::-webkit-scrollbar {
    width: 6px;
}

.track-list::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: 3px;
}

.track-list::-webkit-scrollbar-thumb {
    background: var(--accent-orange);
    border-radius: 3px;
}

.track-list::-webkit-scrollbar-thumb:hover {
    background: #e67c3a;
}

/* Right Sidebar */
.right-sidebar {
    width: 300px;
    background: var(--bg-secondary);
    padding: 20px;
    border-left: 1px solid var(--border-color);
    overflow-y: auto;
}

.widget {
    margin-bottom: 30px;
}

.widget-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

/* Tags */
.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.tag:hover {
    background: var(--bg-card);
}

.tag i {
    font-size: 10px;
    opacity: 0.7;
}

/* Podcast Grid */
.podcast-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.podcast-card {
    aspect-ratio: 1;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.podcast-card:hover {
    transform: scale(1.05);
}

.podcast-card.blue {
    background: linear-gradient(135deg, var(--accent-blue) 0%, #357abd 100%);
}

.podcast-card.purple {
    background: linear-gradient(135deg, var(--accent-purple) 0%, #7c3aed 100%);
}

.podcast-card.teal {
    background: linear-gradient(135deg, var(--accent-teal) 0%, #0d9488 100%);
}

/* Featured Podcast */
.featured-podcast {
    background: var(--bg-card);
    border-radius: var(--radius);
    padding: 16px;
}

.podcast-info {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.podcast-info img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
}

.podcast-details h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
}

.rating i {
    color: #fbbf24;
    font-size: 12px;
}

.rating span {
    font-size: 12px;
    color: var(--text-secondary);
    margin-left: 4px;
}

.podcast-details p {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.play-btn-small,
.remind-btn {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
}

.play-btn-small {
    background: var(--accent-orange);
    color: white;
}

.play-btn-small:hover {
    background: #e67c3a;
}

.remind-btn {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.remind-btn:hover {
    background: var(--bg-primary);
}

/* Bottom Player */
.bottom-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 90px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
}

.player-track-info {
    display: flex;
    align-items: center;
    gap: 16px;
    width: 300px;
}

.player-track-info img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: var(--bg-tertiary);
}

.track-details {
    flex: 1;
}

.track-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.track-artist {
    font-size: 12px;
    color: var(--text-secondary);
}

.player-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: center;
}

.control-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
}

.control-btn:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.play-btn {
    width: 40px;
    height: 40px;
    background: var(--accent-orange);
    color: white;
    border-radius: 50%;
    font-size: 18px;
}

.play-btn:hover {
    background: #e67c3a;
    transform: scale(1.05);
}

.player-extras {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 300px;
    justify-content: flex-end;
}

.volume-slider {
    width: 100px;
}

.volume-slider input {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: var(--accent-orange);
    border-radius: 50%;
    cursor: pointer;
}

.volume-slider input::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: var(--accent-orange);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Progress Bar */
.progress-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    padding: 0 20px;
}

.time-current,
.time-total {
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 35px;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    margin: 0 12px;
}

.progress-fill {
    height: 100%;
    background: var(--accent-orange);
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--accent-orange);
    border-radius: 50%;
    transform: translate(50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .right-sidebar {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: 60px;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 20px;
    }

    .right-sidebar {
        display: none;
    }

    .header-actions .search-box input {
        width: 200px;
    }
}
