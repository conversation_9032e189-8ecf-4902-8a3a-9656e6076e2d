/* ===== MODERN MUSIC PLAYER - MAIN STYLES ===== */

:root {
    /* Colors */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #818cf8;
    --secondary: #f59e0b;
    --accent: #10b981;
    --danger: #ef4444;
    
    /* Backgrounds */
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #262626;
    --bg-card: #1f1f1f;
    --bg-hover: #2a2a2a;
    --bg-active: #333333;
    
    /* Text */
    --text-primary: #ffffff;
    --text-secondary: #a3a3a3;
    --text-muted: #737373;
    --text-inverse: #000000;
    
    /* Borders */
    --border-primary: #404040;
    --border-secondary: #525252;
    --border-accent: var(--primary);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --player-height: 90px;
    --top-bar-height: 70px;
    --right-panel-width: 350px;
}

/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    user-select: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--border-primary);
    border-radius: var(--radius-full);
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-secondary);
}

/* ===== MAIN LAYOUT ===== */
.app {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

.main-app {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr var(--right-panel-width);
    grid-template-rows: 1fr var(--player-height);
    grid-template-areas: 
        "sidebar main-content right-panel"
        "player-bar player-bar player-bar";
    height: 100vh;
    transition: var(--transition-normal);
}

.main-app.sidebar-collapsed {
    grid-template-columns: var(--sidebar-collapsed-width) 1fr var(--right-panel-width);
}

.main-app.panel-hidden {
    grid-template-columns: var(--sidebar-width) 1fr;
}

.main-app.sidebar-collapsed.panel-hidden {
    grid-template-columns: var(--sidebar-collapsed-width) 1fr;
}

.hidden {
    display: none !important;
}

/* ===== SIDEBAR ===== */
.sidebar {
    grid-area: sidebar;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: var(--transition-normal);
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--top-bar-height);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

.logo i {
    font-size: 1.75rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.nav-section {
    margin-bottom: var(--spacing-xl);
}

.nav-section h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-md);
}

.nav-section ul {
    list-style: none;
}

.nav-section li {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    font-weight: 500;
}

.nav-link:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.nav-link.active {
    background: var(--primary);
    color: var(--text-inverse);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
}

.import-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--primary);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.import-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    grid-area: main-content;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--bg-primary);
}

.top-bar {
    height: var(--top-bar-height);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 1;
}

.navigation-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.nav-btn {
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.nav-btn:hover:not(:disabled) {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.search-container {
    position: relative;
    max-width: 500px;
    flex: 1;
}

.search-input {
    width: 100%;
    height: 40px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    padding: 0 var(--spacing-lg) 0 3rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: var(--shadow-glow);
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

.search-clear {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    opacity: 0;
    transition: var(--transition-fast);
}

.search-clear.visible {
    opacity: 1;
}

.search-clear:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.top-bar-right {
    display: flex;
    align-items: center;
}

.user-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.control-btn {
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.control-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.user-profile:hover {
    background: var(--bg-hover);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
}

/* ===== CONTENT CONTAINER ===== */
.content-container {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

.content-view {
    display: none;
    padding: var(--spacing-xl);
    min-height: 100%;
}

.content-view.active {
    display: block;
}
