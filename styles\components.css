/* ===== COMPONENT STYLES ===== */

/* ===== WELCOME SECTION ===== */
.welcome-section {
    margin-bottom: var(--spacing-2xl);
}

.welcome-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-section p {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

/* ===== QUICK ACTIONS ===== */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.quick-action-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: left;
    color: var(--text-primary);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.quick-action-btn:hover {
    background: var(--bg-hover);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.quick-action-btn i {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.quick-action-btn span {
    font-size: 1.25rem;
    font-weight: 600;
}

.quick-action-btn p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* ===== RECENT SECTION ===== */
.recent-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.recent-tracks {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

/* ===== VIEW HEADER ===== */
.view-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
}

.view-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.view-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.sort-select {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
}

.view-toggle {
    display: flex;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.view-toggle-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
}

.view-toggle-btn.active {
    background: var(--primary);
    color: var(--text-inverse);
}

.view-toggle-btn:hover:not(.active) {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* ===== MUSIC GRID ===== */
.music-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.music-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.music-card:hover {
    background: var(--bg-hover);
    border-color: var(--primary);
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.music-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.music-card:hover::before {
    transform: scaleX(1);
}

.card-artwork {
    width: 100%;
    aspect-ratio: 1;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.card-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-artwork .play-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-fast);
}

.music-card:hover .play-overlay {
    opacity: 1;
}

.play-overlay-btn {
    width: 60px;
    height: 60px;
    background: var(--primary);
    border: none;
    border-radius: var(--radius-full);
    color: var(--text-inverse);
    font-size: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.play-overlay-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.card-info {
    text-align: center;
}

.card-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-artist {
    color: var(--text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ===== RIGHT PANEL ===== */
.right-panel {
    grid-area: right-panel;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-primary);
    background: var(--bg-tertiary);
}

.panel-tab {
    flex: 1;
    background: none;
    border: none;
    padding: var(--spacing-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.75rem;
    font-weight: 500;
}

.panel-tab.active {
    background: var(--bg-secondary);
    color: var(--primary);
    border-bottom: 2px solid var(--primary);
}

.panel-tab:hover:not(.active) {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.panel-tab i {
    font-size: 1.25rem;
}

.panel-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.tab-content {
    display: none;
    height: 100%;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* ===== VISUALIZER ===== */
.visualizer-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.visualizer-canvas {
    flex: 1;
    background: radial-gradient(circle at center, rgba(99, 102, 241, 0.1), transparent);
    border-radius: var(--radius-lg);
    margin: var(--spacing-lg);
}

.visualizer-controls {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
}

.viz-mode-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: var(--transition-fast);
}

.viz-mode-btn.active {
    background: var(--primary);
    color: var(--text-inverse);
    border-color: var(--primary);
}

.viz-mode-btn:hover:not(.active) {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* ===== QUEUE ===== */
.queue-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
}

.queue-header h3 {
    font-weight: 600;
    color: var(--text-primary);
}

.clear-queue-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.clear-queue-btn:hover {
    background: var(--bg-hover);
    color: var(--danger);
}

.queue-list {
    padding: var(--spacing-md);
}

.queue-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    margin-bottom: var(--spacing-sm);
}

.queue-item:hover {
    background: var(--bg-hover);
}

.queue-item.current {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid var(--primary);
}

.queue-artwork {
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.queue-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.queue-info {
    flex: 1;
    min-width: 0;
}

.queue-title {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
}

.queue-artist {
    color: var(--text-secondary);
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.queue-duration {
    color: var(--text-muted);
    font-size: 0.75rem;
    font-family: 'JetBrains Mono', monospace;
}

/* ===== LYRICS ===== */
.lyrics-container {
    padding: var(--spacing-xl);
    text-align: center;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-lyrics {
    color: var(--text-muted);
    font-style: italic;
}

.lyrics-content {
    line-height: 2;
    color: var(--text-secondary);
    max-width: 100%;
}

.lyrics-line {
    margin-bottom: var(--spacing-md);
    transition: var(--transition-fast);
}

.lyrics-line.current {
    color: var(--primary);
    font-weight: 600;
    transform: scale(1.05);
}

/* ===== PLAYER BAR ===== */
.player-bar {
    grid-area: player-bar;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    align-items: center;
    padding: 0 var(--spacing-lg);
    gap: var(--spacing-lg);
    backdrop-filter: blur(10px);
}

.player-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 0;
}

.track-artwork {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--bg-tertiary);
    cursor: pointer;
}

.track-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.artwork-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-fast);
}

.track-artwork:hover .artwork-overlay {
    opacity: 1;
}

.artwork-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.25rem;
    cursor: pointer;
}

.track-details {
    flex: 1;
    min-width: 0;
}

.track-title {
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: var(--spacing-xs);
}

.track-artist {
    color: var(--text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.favorite-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.favorite-btn:hover {
    background: var(--bg-hover);
    color: var(--danger);
}

.favorite-btn.active {
    color: var(--danger);
}

.player-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.control-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.control-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.control-btn.active {
    color: var(--primary);
}

.play-pause-btn {
    width: 48px;
    height: 48px;
    background: var(--primary);
    color: var(--text-inverse);
    border-radius: var(--radius-full);
    font-size: 1.25rem;
}

.play-pause-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.progress-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    width: 100%;
}

.time-display {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'JetBrains Mono', monospace;
    min-width: 40px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    position: relative;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: var(--primary);
    border-radius: var(--radius-full);
    transition: width 0.1s ease;
    position: relative;
}

.progress-handle {
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: var(--primary);
    border-radius: var(--radius-full);
    opacity: 0;
    transition: var(--transition-fast);
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.player-extras {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

.volume-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.volume-slider {
    width: 80px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    position: relative;
    cursor: pointer;
}

.volume-fill {
    height: 100%;
    background: var(--primary);
    border-radius: var(--radius-full);
    transition: width 0.1s ease;
    position: relative;
}

.volume-handle {
    position: absolute;
    right: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: var(--primary);
    border-radius: var(--radius-full);
    opacity: 0;
    transition: var(--transition-fast);
}

.volume-container:hover .volume-handle {
    opacity: 1;
}

/* ===== MUSIC LIST VIEW ===== */
.music-list {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.music-list-item {
    display: grid;
    grid-template-columns: 40px 50px 1fr 200px 60px 80px;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-card);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    gap: var(--spacing-md);
}

.music-list-item:hover {
    background: var(--bg-hover);
    transform: translateX(2px);
}

.music-list-item.playing {
    background: rgba(99, 102, 241, 0.1);
    border-left: 3px solid var(--primary);
}

.list-item-index {
    color: var(--text-muted);
    font-size: 0.875rem;
    text-align: center;
    font-family: 'JetBrains Mono', monospace;
}

.list-item-artwork {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
    overflow: hidden;
    background: var(--bg-tertiary);
}

.list-item-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.list-item-info {
    min-width: 0;
}

.list-item-title {
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: var(--spacing-xs);
}

.list-item-artist {
    color: var(--text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-item-album {
    color: var(--text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-item-duration {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-family: 'JetBrains Mono', monospace;
    text-align: right;
}

.list-item-actions {
    display: flex;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: var(--transition-fast);
}

.music-list-item:hover .list-item-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.action-btn.active {
    color: var(--danger);
}

/* ===== EMPTY STATE ===== */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    min-height: 300px;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
}

.empty-state button {
    background: var(--primary);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.empty-state button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* ===== NOTIFICATIONS ===== */
.notifications-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    transform: translateX(100%);
    opacity: 0;
    transition: var(--transition-normal);
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-info {
    border-left: 4px solid var(--primary);
}

.notification-success {
    border-left: 4px solid var(--accent);
}

.notification-error {
    border-left: 4px solid var(--danger);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.notification-message {
    color: var(--text-primary);
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.notification-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* ===== MODAL ===== */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
}

.modal-overlay.hide {
    opacity: 0;
}

.modal-content {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition-normal);
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

/* ===== LOADING STATES ===== */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--primary);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .music-list-item {
        grid-template-columns: 40px 50px 1fr 60px;
    }

    .list-item-album {
        display: none;
    }

    .list-item-actions {
        display: none;
    }
}

@media (max-width: 768px) {
    .music-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: var(--spacing-md);
    }

    .music-list-item {
        grid-template-columns: 50px 1fr 60px;
        gap: var(--spacing-sm);
    }

    .list-item-index {
        display: none;
    }

    .notifications-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
        top: var(--spacing-md);
    }

    .notification {
        max-width: none;
    }
}
