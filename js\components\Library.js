// ===== LIBRARY COMPONENT =====

import { APP_CONFIG } from '../utils/constants.js';
import { TimeUtils, DOMUtils, FileUtils } from '../utils/helpers.js';

export class Library {
    constructor(musicLibrary, playbackManager) {
        this.musicLibrary = musicLibrary;
        this.playbackManager = playbackManager;
        
        // UI Elements
        this.musicGrid = null;
        this.searchInput = null;
        this.sortSelect = null;
        this.viewToggleButtons = null;
        this.importButton = null;
        this.fileInput = null;
        
        // State
        this.currentView = 'grid';
        this.currentSort = 'title';
        this.currentFilter = '';
        this.isLoading = false;
        
        // Virtual scrolling for performance
        this.virtualScrolling = {
            enabled: false,
            itemHeight: 200,
            visibleItems: 0,
            scrollTop: 0,
            totalHeight: 0
        };
        
        this.initialize();
    }

    /**
     * Initialize library component
     */
    initialize() {
        this.setupElements();
        this.setupEventListeners();
        this.render();
        
        console.log('Library component initialized');
    }

    /**
     * Setup UI elements
     */
    setupElements() {
        this.musicGrid = document.getElementById('music-grid');
        this.searchInput = document.getElementById('search-input');
        this.sortSelect = document.getElementById('sort-select');
        this.viewToggleButtons = document.querySelectorAll('.view-toggle-btn');
        this.importButton = document.getElementById('import-music');
        this.fileInput = document.getElementById('music-file-input');
        
        // Quick action buttons
        const quickImport = document.getElementById('quick-import');
        const quickShuffle = document.getElementById('quick-shuffle');
        
        if (quickImport) {
            quickImport.addEventListener('click', () => this.triggerImport());
        }
        
        if (quickShuffle) {
            quickShuffle.addEventListener('click', () => this.shuffleAll());
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search
        if (this.searchInput) {
            this.searchInput.addEventListener('input', DOMUtils.debounce((e) => {
                this.handleSearch(e.target.value);
            }, APP_CONFIG.ui.debounceDelay));
        }
        
        // Sort
        if (this.sortSelect) {
            this.sortSelect.addEventListener('change', (e) => {
                this.handleSort(e.target.value);
            });
        }
        
        // View toggle
        this.viewToggleButtons?.forEach(btn => {
            btn.addEventListener('click', () => {
                this.setView(btn.dataset.view);
            });
        });
        
        // Import
        if (this.importButton) {
            this.importButton.addEventListener('click', () => this.triggerImport());
        }
        
        if (this.fileInput) {
            this.fileInput.addEventListener('change', (e) => this.handleFileImport(e));
        }
        
        // Music library events
        this.musicLibrary.addEventListener('imported', (e) => {
            this.handleLibraryUpdate(e.detail);
        });
        
        this.musicLibrary.addEventListener('favoritesChanged', () => {
            this.render();
        });
        
        // Playback manager events
        this.playbackManager.addEventListener('trackChanged', (e) => {
            this.updatePlayingState(e.detail.track);
        });
        
        // Virtual scrolling
        if (this.musicGrid) {
            this.musicGrid.addEventListener('scroll', DOMUtils.throttle(() => {
                this.handleScroll();
            }, 16)); // 60fps
        }
    }

    /**
     * Render library content
     */
    render() {
        if (!this.musicGrid) return;
        
        this.showLoading(true);
        
        // Get filtered and sorted tracks
        const tracks = this.getFilteredTracks();
        
        // Clear existing content
        this.musicGrid.innerHTML = '';
        
        if (tracks.length === 0) {
            this.renderEmptyState();
        } else {
            this.renderTracks(tracks);
        }
        
        this.showLoading(false);
        this.updateStats(tracks);
    }

    /**
     * Get filtered and sorted tracks
     */
    getFilteredTracks() {
        let tracks;
        
        if (this.currentFilter) {
            tracks = this.musicLibrary.search(this.currentFilter);
        } else {
            tracks = this.musicLibrary.getTracks();
        }
        
        // Apply sorting
        return this.musicLibrary.getTracks({
            sortBy: this.currentSort,
            sortOrder: 'asc'
        }).filter(track => {
            if (!this.currentFilter) return true;
            return tracks.some(filteredTrack => filteredTrack.id === track.id);
        });
    }

    /**
     * Render tracks based on current view
     */
    renderTracks(tracks) {
        if (this.currentView === 'grid') {
            this.renderGridView(tracks);
        } else {
            this.renderListView(tracks);
        }
    }

    /**
     * Render grid view
     */
    renderGridView(tracks) {
        this.musicGrid.className = 'music-grid';
        
        tracks.forEach(track => {
            const card = this.createTrackCard(track);
            this.musicGrid.appendChild(card);
        });
    }

    /**
     * Render list view
     */
    renderListView(tracks) {
        this.musicGrid.className = 'music-list';
        
        tracks.forEach((track, index) => {
            const item = this.createTrackListItem(track, index);
            this.musicGrid.appendChild(item);
        });
    }

    /**
     * Create track card for grid view
     */
    createTrackCard(track) {
        const card = DOMUtils.createElement('div', {
            className: 'music-card',
            'data-track-id': track.id
        });
        
        // Artwork
        const artwork = DOMUtils.createElement('div', { className: 'card-artwork' });
        const img = DOMUtils.createElement('img', {
            src: track.albumArt?.url || 'https://via.placeholder.com/200',
            alt: `${track.title} artwork`,
            loading: 'lazy'
        });
        
        const playOverlay = DOMUtils.createElement('div', { className: 'play-overlay' });
        const playBtn = DOMUtils.createElement('button', { className: 'play-overlay-btn' });
        playBtn.innerHTML = '<i class="fas fa-play"></i>';
        
        playOverlay.appendChild(playBtn);
        artwork.appendChild(img);
        artwork.appendChild(playOverlay);
        
        // Info
        const info = DOMUtils.createElement('div', { className: 'card-info' });
        const title = DOMUtils.createElement('div', {
            className: 'card-title',
            textContent: track.title,
            title: track.title
        });
        const artist = DOMUtils.createElement('div', {
            className: 'card-artist',
            textContent: track.artist,
            title: track.artist
        });
        
        info.appendChild(title);
        info.appendChild(artist);
        
        card.appendChild(artwork);
        card.appendChild(info);
        
        // Event listeners
        card.addEventListener('click', () => this.playTrack(track));
        card.addEventListener('dblclick', () => this.playTrackImmediately(track));
        
        // Context menu
        card.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e, track);
        });
        
        return card;
    }

    /**
     * Create track list item for list view
     */
    createTrackListItem(track, index) {
        const item = DOMUtils.createElement('div', {
            className: 'music-list-item',
            'data-track-id': track.id
        });
        
        const content = `
            <div class="list-item-index">${index + 1}</div>
            <div class="list-item-artwork">
                <img src="${track.albumArt?.url || 'https://via.placeholder.com/40'}" alt="Artwork">
            </div>
            <div class="list-item-info">
                <div class="list-item-title">${track.title}</div>
                <div class="list-item-artist">${track.artist}</div>
            </div>
            <div class="list-item-album">${track.album}</div>
            <div class="list-item-duration">${TimeUtils.formatTime(track.duration)}</div>
            <div class="list-item-actions">
                <button class="action-btn favorite-btn ${this.musicLibrary.isFavorite(track.id) ? 'active' : ''}" title="Add to favorites">
                    <i class="fas fa-heart"></i>
                </button>
                <button class="action-btn more-btn" title="More options">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        `;
        
        item.innerHTML = content;
        
        // Event listeners
        item.addEventListener('click', () => this.playTrack(track));
        item.addEventListener('dblclick', () => this.playTrackImmediately(track));
        
        const favoriteBtn = item.querySelector('.favorite-btn');
        favoriteBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleFavorite(track);
        });
        
        const moreBtn = item.querySelector('.more-btn');
        moreBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showContextMenu(e, track);
        });
        
        return item;
    }

    /**
     * Render empty state
     */
    renderEmptyState() {
        const emptyState = DOMUtils.createElement('div', { className: 'empty-state' });
        
        if (this.currentFilter) {
            emptyState.innerHTML = `
                <div class="empty-icon">🔍</div>
                <h3>No tracks found</h3>
                <p>Try adjusting your search terms</p>
                <button class="clear-search-btn">Clear Search</button>
            `;
            
            const clearBtn = emptyState.querySelector('.clear-search-btn');
            clearBtn?.addEventListener('click', () => {
                this.clearSearch();
            });
        } else {
            emptyState.innerHTML = `
                <div class="empty-icon">🎵</div>
                <h3>Your library is empty</h3>
                <p>Import your music to get started</p>
                <button class="import-music-btn">Import Music</button>
            `;
            
            const importBtn = emptyState.querySelector('.import-music-btn');
            importBtn?.addEventListener('click', () => {
                this.triggerImport();
            });
        }
        
        this.musicGrid.appendChild(emptyState);
    }

    /**
     * Play track (add to queue)
     */
    async playTrack(track) {
        try {
            const tracks = this.getFilteredTracks();
            const trackIndex = tracks.findIndex(t => t.id === track.id);
            
            this.playbackManager.setQueue(tracks, trackIndex);
            await this.playbackManager.playTrack(track);
        } catch (error) {
            console.error('Failed to play track:', error);
        }
    }

    /**
     * Play track immediately (replace queue)
     */
    async playTrackImmediately(track) {
        try {
            this.playbackManager.setQueue([track], 0);
            await this.playbackManager.playTrack(track);
        } catch (error) {
            console.error('Failed to play track immediately:', error);
        }
    }

    /**
     * Toggle favorite status
     */
    toggleFavorite(track) {
        const isFavorite = this.musicLibrary.isFavorite(track.id);
        if (isFavorite) {
            this.musicLibrary.removeFromFavorites(track.id);
        } else {
            this.musicLibrary.addToFavorites(track.id);
        }
    }

    /**
     * Handle search
     */
    handleSearch(query) {
        this.currentFilter = query.trim();
        this.render();
        
        // Update search clear button
        const clearBtn = document.getElementById('search-clear');
        if (clearBtn) {
            clearBtn.classList.toggle('visible', this.currentFilter.length > 0);
        }
    }

    /**
     * Clear search
     */
    clearSearch() {
        this.currentFilter = '';
        if (this.searchInput) {
            this.searchInput.value = '';
        }
        this.render();
    }

    /**
     * Handle sort change
     */
    handleSort(sortBy) {
        this.currentSort = sortBy;
        this.render();
    }

    /**
     * Set view mode
     */
    setView(view) {
        this.currentView = view;
        
        // Update toggle buttons
        this.viewToggleButtons?.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });
        
        this.render();
    }

    /**
     * Trigger file import
     */
    triggerImport() {
        if (this.fileInput) {
            this.fileInput.click();
        }
    }

    /**
     * Handle file import
     */
    async handleFileImport(event) {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;
        
        this.showLoading(true);
        
        try {
            await this.musicLibrary.importFiles(files);
        } catch (error) {
            console.error('Failed to import files:', error);
            this.showError('Failed to import music files');
        } finally {
            this.showLoading(false);
            // Reset file input
            event.target.value = '';
        }
    }

    /**
     * Handle library update
     */
    handleLibraryUpdate(updateData) {
        this.render();
        this.showSuccess(`Imported ${updateData.tracksCount} tracks`);
    }

    /**
     * Shuffle all tracks
     */
    async shuffleAll() {
        const tracks = this.getFilteredTracks();
        if (tracks.length === 0) return;
        
        // Shuffle tracks
        const shuffledTracks = [...tracks].sort(() => Math.random() - 0.5);
        
        this.playbackManager.setQueue(shuffledTracks, 0);
        this.playbackManager.toggleShuffle(); // Enable shuffle mode
        
        if (shuffledTracks.length > 0) {
            await this.playbackManager.playTrack(shuffledTracks[0]);
        }
    }

    /**
     * Update playing state visual indicator
     */
    updatePlayingState(currentTrack) {
        // Remove previous playing indicators
        const previousPlaying = this.musicGrid?.querySelectorAll('.playing');
        previousPlaying?.forEach(el => el.classList.remove('playing'));
        
        // Add playing indicator to current track
        if (currentTrack) {
            const currentCard = this.musicGrid?.querySelector(`[data-track-id="${currentTrack.id}"]`);
            currentCard?.classList.add('playing');
        }
    }

    /**
     * Show context menu
     */
    showContextMenu(event, track) {
        // TODO: Implement context menu
        console.log('Context menu for track:', track.title);
    }

    /**
     * Update statistics display
     */
    updateStats(tracks) {
        const stats = this.musicLibrary.getStats();
        
        // Update any stats displays in the UI
        const statsElements = document.querySelectorAll('.library-stats');
        statsElements.forEach(el => {
            el.textContent = `${tracks.length} of ${stats.totalTracks} tracks`;
        });
    }

    /**
     * Show loading state
     */
    showLoading(loading) {
        this.isLoading = loading;
        
        if (this.musicGrid) {
            this.musicGrid.classList.toggle('loading', loading);
        }
        
        // Show/hide loading spinner
        const loadingSpinner = document.querySelector('.loading-spinner');
        if (loadingSpinner) {
            loadingSpinner.style.display = loading ? 'block' : 'none';
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        // TODO: Implement toast notifications
        console.log('Success:', message);
    }

    /**
     * Show error message
     */
    showError(message) {
        // TODO: Implement toast notifications
        console.error('Error:', message);
    }

    /**
     * Handle scroll for virtual scrolling
     */
    handleScroll() {
        if (!this.virtualScrolling.enabled) return;
        
        // TODO: Implement virtual scrolling for large libraries
    }
}
