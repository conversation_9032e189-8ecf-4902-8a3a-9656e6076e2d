// ===== NEXUS MUSIC PLAYER - MAIN APPLICATION =====

import { APP_CONFIG } from './utils/constants.js';
import { AudioEngine } from './core/AudioEngine.js';
import { MusicLibrary } from './core/MusicLibrary.js';
import { PlaybackManager } from './core/PlaybackManager.js';
import { Visualizer } from './components/Visualizer.js';
import { Player } from './components/Player.js';
import { Library } from './components/Library.js';
import { UIManager } from './components/UI.js';

class NexusMusicPlayer {
    constructor() {
        // Core components
        this.audioEngine = null;
        this.musicLibrary = null;
        this.playbackManager = null;
        
        // UI components
        this.visualizer = null;
        this.player = null;
        this.library = null;
        this.uiManager = null;
        
        // Application state
        this.isInitialized = false;
        this.isLoading = true;
        
        // Performance monitoring
        this.performanceMetrics = {
            initTime: 0,
            memoryUsage: 0,
            renderTime: 0
        };
        
        this.initialize();
    }

    /**
     * Initialize the application
     */
    async initialize() {
        const startTime = performance.now();
        
        try {
            console.log('🎵 Initializing Nexus Music Player...');
            
            // Show loading screen
            this.showLoadingScreen();
            
            // Initialize core components
            await this.initializeCore();
            
            // Initialize UI components
            await this.initializeUI();
            
            // Setup global event listeners
            this.setupGlobalEventListeners();
            
            // Setup error handling
            this.setupErrorHandling();
            
            // Hide loading screen and show app
            await this.hideLoadingScreen();
            
            this.isInitialized = true;
            this.isLoading = false;
            
            // Calculate initialization time
            this.performanceMetrics.initTime = performance.now() - startTime;
            
            console.log(`✅ Nexus Music Player initialized in ${this.performanceMetrics.initTime.toFixed(2)}ms`);
            
            // Dispatch ready event
            document.dispatchEvent(new CustomEvent('appReady', {
                detail: { app: this }
            }));
            
        } catch (error) {
            console.error('❌ Failed to initialize Nexus Music Player:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Initialize core components
     */
    async initializeCore() {
        console.log('🔧 Initializing core components...');
        
        // Initialize audio engine
        this.audioEngine = new AudioEngine();
        await new Promise(resolve => {
            if (this.audioEngine.isInitialized) {
                resolve();
            } else {
                this.audioEngine.addEventListener('initialized', resolve, { once: true });
            }
        });
        
        // Initialize music library
        this.musicLibrary = new MusicLibrary();

        // Listen for server load events
        this.musicLibrary.addEventListener('serverLoaded', (e) => {
            this.updateLibraryStats(e.detail);
        });

        await new Promise(resolve => {
            this.musicLibrary.addEventListener('initialized', resolve, { once: true });
        });
        
        // Initialize playback manager
        this.playbackManager = new PlaybackManager(this.audioEngine, this.musicLibrary);
        
        console.log('✅ Core components initialized');
    }

    /**
     * Initialize UI components
     */
    async initializeUI() {
        console.log('🎨 Initializing UI components...');
        
        // Initialize UI manager first
        this.uiManager = new UIManager();
        
        // Initialize visualizer
        const visualizerCanvas = document.getElementById('visualizer-canvas');
        if (visualizerCanvas) {
            this.visualizer = new Visualizer(this.audioEngine, visualizerCanvas);
        }
        
        // Initialize player controls
        this.player = new Player(this.audioEngine, this.playbackManager, this.musicLibrary);
        
        // Initialize library component
        this.library = new Library(this.musicLibrary, this.playbackManager);
        
        // Setup visualizer mode controls
        this.setupVisualizerControls();
        
        // Setup queue management
        this.setupQueueManagement();
        
        console.log('✅ UI components initialized');
    }

    /**
     * Setup visualizer controls
     */
    setupVisualizerControls() {
        const vizModeButtons = document.querySelectorAll('.viz-mode-btn');
        vizModeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // Update active button
                vizModeButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Set visualizer mode
                const mode = btn.dataset.mode;
                if (this.visualizer && mode) {
                    this.visualizer.setMode(mode);
                }
            });
        });
    }

    /**
     * Setup queue management
     */
    setupQueueManagement() {
        const queueList = document.getElementById('queue-list');
        const clearQueueBtn = document.getElementById('clear-queue');
        
        // Clear queue button
        clearQueueBtn?.addEventListener('click', () => {
            this.playbackManager.clearQueue();
        });
        
        // Update queue display when queue changes
        this.playbackManager.addEventListener('queueChanged', (e) => {
            this.updateQueueDisplay(e.detail);
        });
        
        // Update queue display on track change
        this.playbackManager.addEventListener('trackChanged', (e) => {
            this.updateCurrentTrackInQueue(e.detail);
        });
    }

    /**
     * Update queue display
     */
    updateQueueDisplay(queueData) {
        const queueList = document.getElementById('queue-list');
        if (!queueList) return;
        
        queueList.innerHTML = '';
        
        if (queueData.queue.length === 0) {
            queueList.innerHTML = '<div class="empty-queue">Queue is empty</div>';
            return;
        }
        
        queueData.queue.forEach((track, index) => {
            const queueItem = document.createElement('div');
            queueItem.className = 'queue-item';
            queueItem.dataset.trackId = track.id;
            
            if (index === queueData.index) {
                queueItem.classList.add('current');
            }
            
            queueItem.innerHTML = `
                <div class="queue-artwork">
                    <img src="${track.albumArt?.url || 'https://via.placeholder.com/40'}" alt="Artwork">
                </div>
                <div class="queue-info">
                    <div class="queue-title">${track.title}</div>
                    <div class="queue-artist">${track.artist}</div>
                </div>
                <div class="queue-duration">${this.formatTime(track.duration)}</div>
            `;
            
            // Click to play
            queueItem.addEventListener('click', async () => {
                try {
                    await this.playbackManager.playTrack(track);
                } catch (error) {
                    console.error('Failed to play track from queue:', error);
                }
            });
            
            queueList.appendChild(queueItem);
        });
    }

    /**
     * Update current track in queue display
     */
    updateCurrentTrackInQueue(trackData) {
        const queueItems = document.querySelectorAll('.queue-item');
        queueItems.forEach(item => {
            const isCurrentTrack = item.dataset.trackId === trackData.track.id;
            item.classList.toggle('current', isCurrentTrack);
        });
    }

    /**
     * Update library statistics display
     */
    updateLibraryStats(stats) {
        const libraryStatsEl = document.getElementById('library-stats');
        if (libraryStatsEl) {
            libraryStatsEl.innerHTML = `
                <span class="stat-item">🎵 ${stats.tracksCount} tracks</span>
                <span class="stat-item">👨‍🎤 ${stats.artistsCount} artists</span>
                <span class="stat-item">💿 ${stats.albumsCount} albums</span>
            `;
        }

        // Update artists grid
        this.updateArtistsGrid();

        // Show success notification
        if (this.uiManager) {
            this.uiManager.showNotification(
                `Loaded ${stats.tracksCount} tracks from MUSIC folder`,
                'success'
            );
        }
    }

    /**
     * Update artists grid on home page
     */
    updateArtistsGrid() {
        const artistsGrid = document.getElementById('artists-grid');
        if (!artistsGrid) return;

        const artists = this.musicLibrary.getArtists();
        artistsGrid.innerHTML = '';

        if (artists.length === 0) {
            artistsGrid.innerHTML = '<p class="empty-message">No artists found</p>';
            return;
        }

        artists.slice(0, 6).forEach(artist => { // Show first 6 artists
            const artistCard = document.createElement('div');
            artistCard.className = 'artist-card';

            const albumArt = artist.albumArt ? artist.albumArt.url : 'https://via.placeholder.com/150';

            artistCard.innerHTML = `
                <div class="artist-artwork">
                    <img src="${albumArt}" alt="${artist.name}" loading="lazy">
                    <div class="artist-overlay">
                        <button class="artist-play-btn">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="artist-info">
                    <h3 class="artist-name">${artist.name}</h3>
                    <p class="artist-stats">${artist.tracks.length} tracks</p>
                </div>
            `;

            // Click to view artist
            artistCard.addEventListener('click', () => {
                // Navigate to library view filtered by artist
                this.uiManager.navigateToView('library');
                // TODO: Filter by artist
            });

            artistsGrid.appendChild(artistCard);
        });
    }

    /**
     * Setup global event listeners
     */
    setupGlobalEventListeners() {
        // Prevent default drag and drop behavior
        document.addEventListener('dragover', (e) => e.preventDefault());
        document.addEventListener('drop', (e) => e.preventDefault());
        
        // Handle file drops
        document.addEventListener('drop', async (e) => {
            e.preventDefault();
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                await this.handleFilesDrop(files);
            }
        });
        
        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Pause visualizer when tab is hidden
                this.visualizer?.stop();
            } else {
                // Resume visualizer when tab is visible
                this.visualizer?.start();
            }
        });
        
        // Handle before unload
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // Handle memory warnings
        if ('memory' in performance) {
            setInterval(() => {
                this.monitorMemoryUsage();
            }, 30000); // Check every 30 seconds
        }
    }

    /**
     * Handle dropped files
     */
    async handleFilesDrop(files) {
        try {
            this.uiManager.showNotification('Importing music files...', 'info');
            await this.musicLibrary.importFiles(files);
            this.uiManager.showNotification(`Imported ${files.length} files successfully`, 'success');
        } catch (error) {
            console.error('Failed to import dropped files:', error);
            this.uiManager.showNotification('Failed to import files', 'error');
        }
    }

    /**
     * Setup error handling
     */
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.handleError(e.error);
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.handleError(e.reason);
        });
        
        // Audio engine errors
        this.audioEngine?.addEventListener('error', (e) => {
            this.handleAudioError(e.detail);
        });
    }

    /**
     * Handle general errors
     */
    handleError(error) {
        console.error('Application error:', error);
        
        // Show user-friendly error message
        this.uiManager?.showNotification(
            'An error occurred. Please try again.',
            'error',
            5000
        );
        
        // Log error for debugging
        this.logError(error);
    }

    /**
     * Handle audio-specific errors
     */
    handleAudioError(errorData) {
        console.error('Audio error:', errorData);
        
        let message = 'Audio playback error occurred';
        
        if (errorData.message) {
            message = errorData.message;
        }
        
        this.uiManager?.showNotification(message, 'error', 5000);
    }

    /**
     * Handle initialization errors
     */
    handleInitializationError(error) {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div class="loading-content">
                    <div class="error-icon">⚠️</div>
                    <h2>Failed to Initialize</h2>
                    <p>There was an error starting the music player.</p>
                    <button onclick="location.reload()" class="retry-btn">Retry</button>
                </div>
            `;
        }
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const mainApp = document.getElementById('main-app');
        
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
        
        if (mainApp) {
            mainApp.classList.add('hidden');
        }
        
        // Animate loading progress
        const progressBar = document.querySelector('.loading-progress');
        if (progressBar) {
            progressBar.style.animation = 'loadingProgress 2s ease-in-out';
        }
    }

    /**
     * Hide loading screen
     */
    async hideLoadingScreen() {
        return new Promise(resolve => {
            const loadingScreen = document.getElementById('loading-screen');
            const mainApp = document.getElementById('main-app');
            
            setTimeout(() => {
                if (loadingScreen) {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
                
                if (mainApp) {
                    mainApp.classList.remove('hidden');
                }
                
                resolve();
            }, 1000);
        });
    }

    /**
     * Monitor memory usage
     */
    monitorMemoryUsage() {
        if ('memory' in performance) {
            const memory = performance.memory;
            this.performanceMetrics.memoryUsage = memory.usedJSHeapSize;
            
            // Warn if memory usage is high
            if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                console.warn('High memory usage detected');
                this.optimizeMemoryUsage();
            }
        }
    }

    /**
     * Optimize memory usage
     */
    optimizeMemoryUsage() {
        // Clear unused object URLs
        if (window.objectURLs) {
            window.objectURLs.forEach(url => URL.revokeObjectURL(url));
            window.objectURLs.clear();
        }
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }

    /**
     * Format time helper
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';
        
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * Log error for debugging
     */
    logError(error) {
        const errorLog = {
            timestamp: new Date().toISOString(),
            message: error.message,
            stack: error.stack,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        // Store in localStorage for debugging
        const logs = JSON.parse(localStorage.getItem('nexus_error_logs') || '[]');
        logs.push(errorLog);
        
        // Keep only last 10 errors
        if (logs.length > 10) {
            logs.splice(0, logs.length - 10);
        }
        
        localStorage.setItem('nexus_error_logs', JSON.stringify(logs));
    }

    /**
     * Get application state
     */
    getState() {
        return {
            isInitialized: this.isInitialized,
            isLoading: this.isLoading,
            audio: this.audioEngine?.getState(),
            playback: this.playbackManager?.getState(),
            library: this.musicLibrary?.getStats(),
            ui: this.uiManager?.getState(),
            performance: this.performanceMetrics
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        console.log('🧹 Cleaning up resources...');
        
        // Stop visualizer
        this.visualizer?.destroy();
        
        // Cleanup audio engine
        this.audioEngine?.destroy();
        
        // Cleanup object URLs
        this.optimizeMemoryUsage();
        
        console.log('✅ Cleanup completed');
    }
}

// Initialize application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.nexusPlayer = new NexusMusicPlayer();
});

// Export for debugging
window.NexusMusicPlayer = NexusMusicPlayer;
