// ===== ADVANCED AUDIO ENGINE =====

import { APP_CONFIG, ERROR_MESSAGES } from '../utils/constants.js';
import { AudioUtils } from '../utils/helpers.js';

export class AudioEngine extends EventTarget {
    constructor() {
        super();
        
        // Audio elements and context
        this.audioElement = null;
        this.audioContext = null;
        this.sourceNode = null;
        this.analyserNode = null;
        this.gainNode = null;
        this.compressorNode = null;
        this.equalizerNodes = [];
        
        // Audio data
        this.frequencyData = null;
        this.timeData = null;
        this.previousFrequencyData = null;
        
        // State
        this.isInitialized = false;
        this.isPlaying = false;
        this.isPaused = false;
        this.volume = APP_CONFIG.audio.defaultVolume;
        this.muted = false;
        this.crossfadeEnabled = true;
        
        // Performance monitoring
        this.performanceMetrics = {
            latency: 0,
            bufferHealth: 0,
            dropouts: 0
        };
        
        this.initialize();
    }

    /**
     * Initialize the audio engine
     */
    async initialize() {
        try {
            // Create audio element
            this.audioElement = document.getElementById('audio-player');
            if (!this.audioElement) {
                this.audioElement = document.createElement('audio');
                this.audioElement.id = 'audio-player';
                this.audioElement.crossOrigin = 'anonymous';
                document.body.appendChild(this.audioElement);
            }

            // Initialize Web Audio API
            await this.initializeWebAudio();
            
            // Setup event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            this.dispatchEvent(new CustomEvent('initialized'));
            
            console.log('Audio Engine initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Audio Engine:', error);
            this.dispatchEvent(new CustomEvent('error', { 
                detail: { message: ERROR_MESSAGES.AUDIO_CONTEXT_FAILED, error } 
            }));
        }
    }

    /**
     * Initialize Web Audio API context and nodes
     */
    async initializeWebAudio() {
        // Create audio context
        this.audioContext = AudioUtils.getAudioContext();
        
        // Resume context if suspended (required by some browsers)
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }

        // Create audio nodes
        this.createAudioNodes();
        
        // Connect audio graph
        this.connectAudioGraph();
    }

    /**
     * Create all audio processing nodes
     */
    createAudioNodes() {
        // Analyser for visualizations
        this.analyserNode = this.audioContext.createAnalyser();
        this.analyserNode.fftSize = APP_CONFIG.visualizer.fftSize;
        this.analyserNode.smoothingTimeConstant = APP_CONFIG.visualizer.smoothingTimeConstant;
        this.analyserNode.minDecibels = APP_CONFIG.visualizer.minDecibels;
        this.analyserNode.maxDecibels = APP_CONFIG.visualizer.maxDecibels;

        // Gain node for volume control
        this.gainNode = this.audioContext.createGain();
        this.gainNode.gain.value = this.volume;

        // Compressor for dynamic range control
        this.compressorNode = this.audioContext.createDynamicsCompressor();
        this.compressorNode.threshold.value = -24;
        this.compressorNode.knee.value = 30;
        this.compressorNode.ratio.value = 12;
        this.compressorNode.attack.value = 0.003;
        this.compressorNode.release.value = 0.25;

        // Create equalizer bands (10-band EQ)
        this.createEqualizer();

        // Initialize frequency and time data arrays
        const bufferLength = this.analyserNode.frequencyBinCount;
        this.frequencyData = new Uint8Array(bufferLength);
        this.timeData = new Uint8Array(bufferLength);
    }

    /**
     * Create 10-band equalizer
     */
    createEqualizer() {
        const frequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000];
        
        this.equalizerNodes = frequencies.map(frequency => {
            const filter = this.audioContext.createBiquadFilter();
            filter.type = frequency === 32 ? 'lowshelf' : 
                         frequency === 16000 ? 'highshelf' : 'peaking';
            filter.frequency.value = frequency;
            filter.Q.value = 1;
            filter.gain.value = 0;
            return filter;
        });
    }

    /**
     * Connect audio processing graph
     */
    connectAudioGraph() {
        // Connect source when available
        if (this.sourceNode) {
            this.sourceNode.disconnect();
        }

        // Create media element source
        this.sourceNode = this.audioContext.createMediaElementSource(this.audioElement);

        // Connect: source -> equalizer -> compressor -> gain -> analyser -> destination
        let currentNode = this.sourceNode;
        
        // Connect equalizer chain
        this.equalizerNodes.forEach(eqNode => {
            currentNode.connect(eqNode);
            currentNode = eqNode;
        });

        // Connect processing chain
        currentNode.connect(this.compressorNode);
        this.compressorNode.connect(this.gainNode);
        this.gainNode.connect(this.analyserNode);
        this.analyserNode.connect(this.audioContext.destination);
    }

    /**
     * Setup audio element event listeners
     */
    setupEventListeners() {
        // Playback events
        this.audioElement.addEventListener('loadstart', () => {
            this.dispatchEvent(new CustomEvent('loadstart'));
        });

        this.audioElement.addEventListener('loadedmetadata', () => {
            this.dispatchEvent(new CustomEvent('loadedmetadata', {
                detail: {
                    duration: this.audioElement.duration,
                    currentTime: this.audioElement.currentTime
                }
            }));
        });

        this.audioElement.addEventListener('canplay', () => {
            this.dispatchEvent(new CustomEvent('canplay'));
        });

        this.audioElement.addEventListener('play', () => {
            this.isPlaying = true;
            this.isPaused = false;
            this.dispatchEvent(new CustomEvent('play'));
        });

        this.audioElement.addEventListener('pause', () => {
            this.isPlaying = false;
            this.isPaused = true;
            this.dispatchEvent(new CustomEvent('pause'));
        });

        this.audioElement.addEventListener('ended', () => {
            this.isPlaying = false;
            this.isPaused = false;
            this.dispatchEvent(new CustomEvent('ended'));
        });

        this.audioElement.addEventListener('timeupdate', () => {
            this.dispatchEvent(new CustomEvent('timeupdate', {
                detail: {
                    currentTime: this.audioElement.currentTime,
                    duration: this.audioElement.duration,
                    progress: this.audioElement.duration ? 
                        this.audioElement.currentTime / this.audioElement.duration : 0
                }
            }));
        });

        this.audioElement.addEventListener('error', (event) => {
            this.dispatchEvent(new CustomEvent('error', {
                detail: { 
                    message: ERROR_MESSAGES.AUDIO_LOAD_FAILED, 
                    error: event.target.error 
                }
            }));
        });

        // Volume change events
        this.audioElement.addEventListener('volumechange', () => {
            this.dispatchEvent(new CustomEvent('volumechange', {
                detail: {
                    volume: this.audioElement.volume,
                    muted: this.audioElement.muted
                }
            }));
        });
    }

    /**
     * Load and play audio file
     */
    async loadTrack(audioUrl) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            // Resume audio context if needed
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            this.audioElement.src = audioUrl;
            this.audioElement.load();

            // Reconnect audio graph if needed
            if (!this.sourceNode) {
                this.connectAudioGraph();
            }

            return new Promise((resolve, reject) => {
                const onCanPlay = () => {
                    this.audioElement.removeEventListener('canplay', onCanPlay);
                    this.audioElement.removeEventListener('error', onError);
                    resolve();
                };

                const onError = (event) => {
                    this.audioElement.removeEventListener('canplay', onCanPlay);
                    this.audioElement.removeEventListener('error', onError);
                    reject(new Error(ERROR_MESSAGES.AUDIO_LOAD_FAILED));
                };

                this.audioElement.addEventListener('canplay', onCanPlay);
                this.audioElement.addEventListener('error', onError);
            });
        } catch (error) {
            console.error('Failed to load track:', error);
            throw error;
        }
    }

    /**
     * Play audio
     */
    async play() {
        try {
            if (!this.audioElement.src) {
                throw new Error('No audio source loaded');
            }

            // Resume audio context if suspended
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            await this.audioElement.play();
        } catch (error) {
            console.error('Failed to play audio:', error);
            this.dispatchEvent(new CustomEvent('error', {
                detail: { message: ERROR_MESSAGES.AUDIO_PLAY_FAILED, error }
            }));
            throw error;
        }
    }

    /**
     * Pause audio
     */
    pause() {
        this.audioElement.pause();
    }

    /**
     * Stop audio
     */
    stop() {
        this.audioElement.pause();
        this.audioElement.currentTime = 0;
    }

    /**
     * Set playback position
     */
    seek(time) {
        if (this.audioElement.duration) {
            this.audioElement.currentTime = Math.max(0, Math.min(time, this.audioElement.duration));
        }
    }

    /**
     * Set volume (0-1)
     */
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        this.audioElement.volume = this.volume;
        
        if (this.gainNode) {
            this.gainNode.gain.setValueAtTime(this.volume, this.audioContext.currentTime);
        }
    }

    /**
     * Toggle mute
     */
    toggleMute() {
        this.muted = !this.muted;
        this.audioElement.muted = this.muted;
        return this.muted;
    }

    /**
     * Set equalizer band gain
     */
    setEqualizerGain(bandIndex, gain) {
        if (this.equalizerNodes[bandIndex]) {
            this.equalizerNodes[bandIndex].gain.setValueAtTime(
                gain, 
                this.audioContext.currentTime
            );
        }
    }

    /**
     * Get current frequency data for visualizations
     */
    getFrequencyData() {
        if (this.analyserNode) {
            this.analyserNode.getByteFrequencyData(this.frequencyData);
            
            // Apply smoothing
            if (this.previousFrequencyData) {
                this.frequencyData = AudioUtils.smoothFrequencyData(
                    this.frequencyData, 
                    this.previousFrequencyData,
                    APP_CONFIG.visualizer.smoothingTimeConstant
                );
            }
            
            this.previousFrequencyData = new Uint8Array(this.frequencyData);
        }
        return this.frequencyData;
    }

    /**
     * Get current time domain data
     */
    getTimeData() {
        if (this.analyserNode) {
            this.analyserNode.getByteTimeDomainData(this.timeData);
        }
        return this.timeData;
    }

    /**
     * Get current playback state
     */
    getState() {
        return {
            isPlaying: this.isPlaying,
            isPaused: this.isPaused,
            currentTime: this.audioElement?.currentTime || 0,
            duration: this.audioElement?.duration || 0,
            volume: this.volume,
            muted: this.muted,
            buffered: this.getBufferedRanges()
        };
    }

    /**
     * Get buffered time ranges
     */
    getBufferedRanges() {
        const buffered = this.audioElement?.buffered;
        const ranges = [];
        
        if (buffered) {
            for (let i = 0; i < buffered.length; i++) {
                ranges.push({
                    start: buffered.start(i),
                    end: buffered.end(i)
                });
            }
        }
        
        return ranges;
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.audioElement) {
            this.audioElement.pause();
            this.audioElement.src = '';
        }

        if (this.sourceNode) {
            this.sourceNode.disconnect();
        }

        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }

        this.isInitialized = false;
    }
}
