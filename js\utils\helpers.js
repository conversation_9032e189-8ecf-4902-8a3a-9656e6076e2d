// ===== UTILITY HELPER FUNCTIONS =====

import { SUPPORTED_FORMATS, SUPPORTED_IMAGE_FORMATS, REGEX_PATTERNS } from './constants.js';

// ===== TIME UTILITIES =====
export const TimeUtils = {
    /**
     * Format seconds to MM:SS or HH:MM:SS format
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },

    /**
     * Parse time string to seconds
     */
    parseTime(timeString) {
        const match = timeString.match(REGEX_PATTERNS.TIME_FORMAT);
        if (!match) return 0;
        
        const minutes = parseInt(match[1], 10);
        const seconds = parseInt(match[2], 10);
        return minutes * 60 + seconds;
    },

    /**
     * Get relative time (e.g., "2 hours ago")
     */
    getRelativeTime(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'Just now';
    }
};

// ===== FILE UTILITIES =====
export const FileUtils = {
    /**
     * Check if file is supported audio format
     */
    isAudioFile(file) {
        return SUPPORTED_FORMATS.includes(file.type) || 
               this.getFileExtension(file.name).toLowerCase() in ['.mp3', '.m4a', '.wav', '.ogg', '.flac'];
    },

    /**
     * Check if file is supported image format
     */
    isImageFile(file) {
        return SUPPORTED_IMAGE_FORMATS.includes(file.type);
    },

    /**
     * Get file extension
     */
    getFileExtension(filename) {
        return filename.slice(filename.lastIndexOf('.')).toLowerCase();
    },

    /**
     * Get file size in human readable format
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Extract metadata from file path
     */
    extractMetadataFromPath(filePath) {
        const pathParts = filePath.split('/');
        const filename = pathParts[pathParts.length - 1];
        const cleanTitle = filename.replace(/\.[^/.]+$/, ''); // Remove extension
        
        let artist = 'Unknown Artist';
        let album = 'Unknown Album';
        let title = cleanTitle;

        // Try to extract from path structure: MUSIC/ARTISTS/Artist/Albums/Album/track.mp3
        if (pathParts.length >= 5 && pathParts[1] === 'ARTISTS') {
            artist = pathParts[2];
            album = pathParts[4];
        }
        // Try to extract from filename: Artist - Title
        else if (cleanTitle.includes(' - ')) {
            const parts = cleanTitle.split(' - ');
            if (parts.length >= 2) {
                artist = parts[0].trim();
                title = parts.slice(1).join(' - ').trim();
            }
        }

        // Clean up track number from title
        const trackMatch = title.match(REGEX_PATTERNS.TRACK_NUMBER);
        if (trackMatch) {
            title = trackMatch[2].trim();
        }

        return { artist, album, title };
    },

    /**
     * Create object URL with cleanup tracking
     */
    createObjectURL(file) {
        const url = URL.createObjectURL(file);
        // Store for cleanup later
        if (!window.objectURLs) window.objectURLs = new Set();
        window.objectURLs.add(url);
        return url;
    },

    /**
     * Cleanup object URLs
     */
    cleanupObjectURLs() {
        if (window.objectURLs) {
            window.objectURLs.forEach(url => URL.revokeObjectURL(url));
            window.objectURLs.clear();
        }
    }
};

// ===== DOM UTILITIES =====
export const DOMUtils = {
    /**
     * Create element with attributes and children
     */
    createElement(tag, attributes = {}, children = []) {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'innerHTML') {
                element.innerHTML = value;
            } else if (key === 'textContent') {
                element.textContent = value;
            } else {
                element.setAttribute(key, value);
            }
        });

        children.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });

        return element;
    },

    /**
     * Add event listener with cleanup tracking
     */
    addEventListener(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
        
        // Track for cleanup
        if (!element._eventListeners) element._eventListeners = [];
        element._eventListeners.push({ event, handler, options });
    },

    /**
     * Remove all event listeners from element
     */
    removeAllEventListeners(element) {
        if (element._eventListeners) {
            element._eventListeners.forEach(({ event, handler, options }) => {
                element.removeEventListener(event, handler, options);
            });
            element._eventListeners = [];
        }
    },

    /**
     * Animate element with CSS transitions
     */
    animate(element, properties, duration = 300, easing = 'ease') {
        return new Promise(resolve => {
            const originalTransition = element.style.transition;
            element.style.transition = `all ${duration}ms ${easing}`;
            
            Object.entries(properties).forEach(([prop, value]) => {
                element.style[prop] = value;
            });

            setTimeout(() => {
                element.style.transition = originalTransition;
                resolve();
            }, duration);
        });
    },

    /**
     * Debounce function calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Throttle function calls
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// ===== STORAGE UTILITIES =====
export const StorageUtils = {
    /**
     * Save data to localStorage with error handling
     */
    save(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
            return false;
        }
    },

    /**
     * Load data from localStorage with error handling
     */
    load(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            return defaultValue;
        }
    },

    /**
     * Remove data from localStorage
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Failed to remove from localStorage:', error);
            return false;
        }
    },

    /**
     * Clear all app data from localStorage
     */
    clear() {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('nexus_')) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('Failed to clear localStorage:', error);
            return false;
        }
    },

    /**
     * Get storage usage information
     */
    getStorageInfo() {
        let totalSize = 0;
        let appSize = 0;
        
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                const size = localStorage[key].length;
                totalSize += size;
                if (key.startsWith('nexus_')) {
                    appSize += size;
                }
            }
        }
        
        return {
            totalSize: totalSize,
            appSize: appSize,
            available: 5 * 1024 * 1024 - totalSize, // Assume 5MB limit
            percentage: (totalSize / (5 * 1024 * 1024)) * 100
        };
    }
};

// ===== AUDIO UTILITIES =====
export const AudioUtils = {
    /**
     * Get audio context with fallbacks
     */
    getAudioContext() {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (!AudioContext) {
            throw new Error('Web Audio API not supported');
        }
        return new AudioContext();
    },

    /**
     * Calculate RMS (Root Mean Square) for audio data
     */
    calculateRMS(dataArray) {
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
            sum += dataArray[i] * dataArray[i];
        }
        return Math.sqrt(sum / dataArray.length);
    },

    /**
     * Apply smoothing to frequency data
     */
    smoothFrequencyData(currentData, previousData, smoothing = 0.8) {
        if (!previousData) return currentData;
        
        const smoothedData = new Uint8Array(currentData.length);
        for (let i = 0; i < currentData.length; i++) {
            smoothedData[i] = previousData[i] * smoothing + currentData[i] * (1 - smoothing);
        }
        return smoothedData;
    },

    /**
     * Convert frequency to musical note
     */
    frequencyToNote(frequency) {
        const A4 = 440;
        const C0 = A4 * Math.pow(2, -4.75);
        const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
        
        if (frequency > 0) {
            const h = Math.round(12 * Math.log2(frequency / C0));
            const octave = Math.floor(h / 12);
            const n = h % 12;
            return noteNames[n] + octave;
        }
        return '';
    }
};

// ===== MATH UTILITIES =====
export const MathUtils = {
    /**
     * Linear interpolation
     */
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },

    /**
     * Clamp value between min and max
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    /**
     * Map value from one range to another
     */
    map(value, inMin, inMax, outMin, outMax) {
        return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
    },

    /**
     * Generate random number between min and max
     */
    random(min = 0, max = 1) {
        return Math.random() * (max - min) + min;
    },

    /**
     * Generate random integer between min and max (inclusive)
     */
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
};
