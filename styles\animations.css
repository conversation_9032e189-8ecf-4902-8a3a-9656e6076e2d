/* ===== ANIMATIONS & TRANSITIONS ===== */

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    inset: 0;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeOut 0.5s ease 2s forwards;
}

.loading-content {
    text-align: center;
    animation: slideUp 0.8s ease;
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: var(--spacing-xl);
}

.loading-logo i {
    font-size: 3rem;
    animation: pulse 2s ease-in-out infinite;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: var(--radius-full);
    animation: loadingProgress 2s ease-in-out;
}

.loading-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    animation: fadeInOut 1.5s ease-in-out infinite;
}

/* ===== KEYFRAMES ===== */
@keyframes fadeOut {
    to {
        opacity: 0;
        visibility: hidden;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes loadingProgress {
    0% {
        width: 0%;
    }
    50% {
        width: 70%;
    }
    100% {
        width: 100%;
    }
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0.5;
    }
    50% {
        opacity: 1;
    }
}

/* ===== HOVER ANIMATIONS ===== */
.music-card {
    transform-origin: center bottom;
}

.music-card:hover {
    animation: cardHover 0.3s ease;
}

@keyframes cardHover {
    0% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-2px) scale(1.02);
    }
    100% {
        transform: translateY(-4px) scale(1);
    }
}

/* ===== BUTTON ANIMATIONS ===== */
.control-btn {
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.control-btn:active::before {
    width: 100%;
    height: 100%;
}

/* ===== PLAY BUTTON ANIMATION ===== */
.play-pause-btn {
    position: relative;
    overflow: hidden;
}

.play-pause-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-fast);
}

.play-pause-btn:hover::after {
    opacity: 1;
    animation: ripple 0.6s ease;
}

@keyframes ripple {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* ===== PROGRESS BAR ANIMATIONS ===== */
.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.progress-bar:hover::before {
    left: 100%;
}

/* ===== SIDEBAR ANIMATIONS ===== */
.sidebar {
    transition: width var(--transition-normal) ease;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .logo span,
.sidebar.collapsed .nav-link span,
.sidebar.collapsed .import-btn span,
.sidebar.collapsed .nav-section h3 {
    opacity: 0;
    transform: translateX(-10px);
    transition: var(--transition-fast);
}

.sidebar:not(.collapsed) .logo span,
.sidebar:not(.collapsed) .nav-link span,
.sidebar:not(.collapsed) .import-btn span,
.sidebar:not(.collapsed) .nav-section h3 {
    opacity: 1;
    transform: translateX(0);
    transition: var(--transition-normal) 0.1s;
}

/* ===== SEARCH ANIMATIONS ===== */
.search-input:focus {
    animation: searchFocus 0.3s ease;
}

@keyframes searchFocus {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    100% {
        box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    }
}

/* ===== VISUALIZER ANIMATIONS ===== */
.visualizer-canvas {
    position: relative;
    overflow: hidden;
}

.visualizer-canvas::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, rgba(99, 102, 241, 0.1), transparent);
    animation: visualizerPulse 4s ease-in-out infinite;
}

@keyframes visualizerPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

/* ===== QUEUE ANIMATIONS ===== */
.queue-item {
    animation: slideInRight 0.3s ease;
}

.queue-item:nth-child(odd) {
    animation-delay: 0.1s;
}

.queue-item:nth-child(even) {
    animation-delay: 0.2s;
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== CONTENT VIEW TRANSITIONS ===== */
.content-view {
    animation: fadeInUp 0.4s ease;
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== RESPONSIVE ANIMATIONS ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== FLOATING ELEMENTS ===== */
.floating {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* ===== GLOW EFFECTS ===== */
.glow {
    position: relative;
}

.glow::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, var(--primary), var(--primary-light), var(--primary));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: var(--transition-normal);
}

.glow:hover::before {
    opacity: 0.7;
    animation: glowRotate 2s linear infinite;
}

@keyframes glowRotate {
    0% {
        filter: hue-rotate(0deg);
    }
    100% {
        filter: hue-rotate(360deg);
    }
}
